// --- START OF FILE background.js ---

// --- Content Script Injection System ---
/**
 * Privacy-friendly content script injection using activeTab permission
 * Scripts are only injected when user explicitly activates the extension
 */

// Content script files in the correct order for injection
const CONTENT_SCRIPT_FILES = [
  // Console wrapper (must load first to affect all subsequent scripts)
  "lib/console-wrapper.js",

  // Core libraries
  "katex.min.js",
  "settings.js",
  "lib/pako.min.js",
  "lib/lz-string-local.js",
  "lib/url-utils.js",
  "lib/security-audit.js",
  "lib/purify.min.js",
  "lib/dompurify-loader.js",
  "lib/sanitization-manager.js",
  "lib/secure-storage.js",
  "lib/subresource-integrity.js",
  "lib/permission-manager.js",
  "lib/secure-messaging.js",
  "lib/enhanced-status-manager.js",
  "lib/performance-optimizer.js",
  "lib/inline-workers.js",
  "lib/worker-manager.js",
  "lib/google-docs-integration.js",
  "lib/premium-manager.js",

  // AI system modules (load before content scripts that use AI)
  "lib/ai-provider-detector.js",
  "lib/universal-ai-adapter.js",
  "lib/google-ai-integration.js",
  "lib/ai-migration-bridge.js",

  // Enhanced content extraction
  "lib/readability.js",
  "content/content-readability.js",

  // Content script modules
  "content/content-state.js",
  "content/content-security-utils.js",
  "content/content-csp.js",
  "content/content-utils.js",
  "content/content-templates.js",
  "content/content-template-manager.js",
  "content/content-smart-templates.js",
  "content/content-table-builder.js",
  "content/content-visual-template-builder.js",
  "content/content-template-editor.js",
  "content/content-toolbar.js",
  "content/content-screenshot.js",
  "content/content-ui.js",
  "content/content-notebook-sidebar.js",
  "content/content-storage.js",
  "content/content-interactions.js",
  "content/content-search.js",
  "content/content-highlighting.js",
  "content/content-image-download.js",
  "content/content-flashcards.js",
  "content/content-voice.js",
  "content/content-voice-enhanced.js",
  "content/content-diagram.js",
  "content/content-equation-editor.js",
  "content/content-page-info.js",
  "content/content-messaging.js",
  "content/content-quick-snippet.js",
  "content/content-ui-customization.js",

  // UI modules
  "ui/dashboard-events.js",
  "ui/google-docs-export.js",
  "ui/dashboard-gdocs-integration.js",
  "ui/direct-gdocs-button.js",
  "content/content-gdocs-button.js",
  "content/content-ai-features.js",
  "content/content-academic-solver.js",
  "content/content-transcript.js",

  // Final modules
  "content/content-rendering-optimization.js",
  "content/content-main.js"
];

// CSS files to inject
const CONTENT_CSS_FILES = [
  "katex.min.css",
  "content.css",
  "ui/google-docs-export.css"
];

/**
 * Checks if content scripts are already injected in a tab
 * @param {number} tabId - The tab ID to check
 * @returns {Promise<boolean>} - True if already injected
 */
async function isContentScriptInjected(tabId) {
  try {
    const results = await chrome.scripting.executeScript({
      target: { tabId: tabId },
      func: () => window.StashyInitialized === true
    });
    return results && results[0] && results[0].result === true;
  } catch (error) {
    // Content script not yet injected in tab
    return false;
  }
}

/**
 * Injects content scripts into a tab using activeTab permission
 * @param {number} tabId - The tab ID to inject into
 * @returns {Promise<boolean>} - True if injection was successful
 */
async function injectContentScripts(tabId) {
  try {
    // Injecting content scripts into tab

    // Check if already injected
    if (await isContentScriptInjected(tabId)) {
      // Content scripts already injected in tab
      return true;
    }

    // Inject CSS files first
    await chrome.scripting.insertCSS({
      target: { tabId: tabId },
      files: CONTENT_CSS_FILES
    });

    // Inject JavaScript files in order
    await chrome.scripting.executeScript({
      target: { tabId: tabId },
      files: CONTENT_SCRIPT_FILES
    });

    // Content script injection completed successfully
    return true;

  } catch (error) {
    console.error("BG: Failed to inject content scripts:", error);

    // Check if it's a permissions error
    if (error.message && error.message.includes('Cannot access')) {
      console.error("BG: Permission denied - user may need to activate extension on this tab");
    }

    return false;
  }
}

/**
 * Handles extension icon click - injects content scripts using activeTab
 */
chrome.action.onClicked.addListener(async (tab) => {
  try {
    console.log("BG: Extension icon clicked for tab", tab.id);

    // Validate tab URL
    if (!tab.url || (!tab.url.startsWith('http://') && !tab.url.startsWith('https://'))) {
      console.warn("BG: Cannot inject content scripts into non-web pages");
      return;
    }

    // Inject content scripts
    const success = await injectContentScripts(tab.id);

    if (success) {
      console.log("BG: Content scripts injected successfully, extension is now active on this tab");

      // Optional: Send a message to the content script to show activation
      try {
        await chrome.tabs.sendMessage(tab.id, {
          action: 'extensionActivated',
          _sourceExtensionId: chrome.runtime.id
        });
      } catch (msgError) {
        // Content script might not be ready yet, that's okay
        console.log("BG: Could not send activation message (content script may still be loading)");
      }
    } else {
      console.error("BG: Failed to inject content scripts");
    }

  } catch (error) {
    console.error("BG: Error handling extension icon click:", error);
  }
});

// --- Utility Functions ---
function stripHtml(html) {
    if (!html || typeof html !== 'string') return '';

    // Enhanced HTML sanitization for background script (no DOM access)
    let sanitized = String(html)
        // Remove script tags and their content
        .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
        // Remove style tags and their content
        .replace(/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/gi, '')
        // Remove iframe, object, embed tags
        .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, '')
        .replace(/<object\b[^<]*(?:(?!<\/object>)<[^<]*)*<\/object>/gi, '')
        .replace(/<embed\b[^<]*(?:(?!<\/embed>)<[^<]*)*<\/embed>/gi, '')
        // Remove form elements
        .replace(/<form\b[^<]*(?:(?!<\/form>)<[^<]*)*<\/form>/gi, '')
        .replace(/<input\b[^>]*>/gi, '')
        .replace(/<textarea\b[^<]*(?:(?!<\/textarea>)<[^<]*)*<\/textarea>/gi, '')
        // Remove javascript: and data: URLs
        .replace(/javascript:/gi, '')
        .replace(/data:/gi, '')
        // Remove event handlers
        .replace(/\son\w+\s*=\s*["'][^"']*["']/gi, '')
        .replace(/\son\w+\s*=\s*[^>\s]+/gi, '')
        // Replace <br> with space
        .replace(/<br\s*\/?>/gi, ' ')
        // Remove all remaining HTML tags
        .replace(/<[^>]*>/g, '')
        // Decode HTML entities
        .replace(/&nbsp;/g, ' ')
        .replace(/&amp;/g, '&')
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/&quot;/g, '"')
        .replace(/&#039;/g, "'")
        // Replace multiple spaces with single space
        .replace(/\s+/g, ' ')
        // Trim whitespace
        .trim();

    return sanitized;
}

// --- Utility Function to convert Data URL to Blob ---
function dataURLtoBlob(dataurl) {
    if (!dataurl) return null;
    try {
        const arr = dataurl.split(',');
        if (arr.length < 2) return null;
        const mimeMatch = arr[0].match(/:(.*?);/);
        if (!mimeMatch || mimeMatch.length < 2) return null;
        const mime = mimeMatch[1];
        const bstr = atob(arr[1]); // Decode base64
        let n = bstr.length;
        const u8arr = new Uint8Array(n);
        while (n--) {
            u8arr[n] = bstr.charCodeAt(n);
        }
        return new Blob([u8arr], { type: mime });
    } catch (e) {
        console.error("BG: Error converting Data URL to Blob", e);
        return null;
    }
}

// --- Utility Function to convert base64 string to Blob ---
function base64ToBlob(base64String, mimeType) {
    try {
        // Decode base64
        const byteCharacters = atob(base64String);
        const byteArrays = [];

        // Slice the byteCharacters into smaller chunks to avoid memory issues
        const sliceSize = 1024;
        for (let offset = 0; offset < byteCharacters.length; offset += sliceSize) {
            const slice = byteCharacters.slice(offset, offset + sliceSize);

            const byteNumbers = new Array(slice.length);
            for (let i = 0; i < slice.length; i++) {
                byteNumbers[i] = slice.charCodeAt(i);
            }

            const byteArray = new Uint8Array(byteNumbers);
            byteArrays.push(byteArray);
        }

        return new Blob(byteArrays, { type: mimeType });
    } catch (e) {
        console.error("BG: Error converting base64 to Blob", e);
        return null;
    }
}

// PDF Export Functions ---
/**
 * Ensures the offscreen document is created
 * @returns {Promise<void>}
 */
async function ensureOffscreenDocument() {
    try {
        // Check if offscreen document is already created
        console.log("BG: Checking for existing offscreen document");
        const existingContexts = await chrome.runtime.getContexts({
            contextTypes: ['OFFSCREEN_DOCUMENT']
        });

        console.log("BG: Existing contexts:", existingContexts);

        if (existingContexts.length > 0) {
            console.log("BG: Offscreen document already exists");
            return;
        }

        // Create offscreen document
        console.log("BG: Creating offscreen document");
        await chrome.offscreen.createDocument({
            url: 'offscreen.html',
            reasons: ['IFRAME_SCRIPTING'],
            justification: 'Used for PDF generation and image processing'
        });

        console.log("BG: Offscreen document created successfully");

        // Wait a moment for the document to initialize
        await new Promise(resolve => setTimeout(resolve, 500));
        console.log("BG: Waited for offscreen document initialization");
    } catch (error) {
        console.error("BG: Error ensuring offscreen document:", error);
        throw error;
    }
}

// PDF generation functionality has been removed

// --- NEW: Canvas Drawing Helpers (for overlays) ---




// --- START: Added Helper Functions for Full Page Capture ---

/** Executes a debugger command and returns a promise */
function sendDebuggerCommand(target, method, params = {}) {
    return new Promise((resolve, reject) => {
        chrome.debugger.sendCommand(target, method, params, (result) => {
            if (chrome.runtime.lastError) {
                reject(new Error(`Debugger command ${method} failed: ${chrome.runtime.lastError.message}`));
            } else if (result?.exceptionDetails) {
                 console.error(`Debugger command ${method} resulted in exception:`, result.exceptionDetails);
                 reject(new Error(`Debugger command ${method} exception: ${result.exceptionDetails.exception?.description || 'Unknown exception'}`));
            } else {
                resolve(result);
            }
        });
    });
}

/** Captures the full page by scrolling and stitching */
async function captureFullPage(target, _windowId, quality) { // Prefix with underscore to indicate unused parameter
    const targetId = { tabId: target.tabId };
    let attached = false;
    try {
        console.log("BG: Attaching debugger for full page capture...", targetId);
        await chrome.debugger.attach(targetId, "1.3");
        attached = true;
        console.log("BG: Debugger attached.");

        // --- Get Device Pixel Ratio ---
        const { result: { value: devicePixelRatio } } = await sendDebuggerCommand(targetId, "Runtime.evaluate", { expression: "window.devicePixelRatio", returnByValue: true });
        console.log("BG: Target page devicePixelRatio:", devicePixelRatio);
        // -----------------------------

        // Get necessary metrics
        const { contentSize, visualViewport } = await sendDebuggerCommand(targetId, "Page.getLayoutMetrics");
        const { clientWidth, clientHeight } = visualViewport;
        const fullHeight = Math.ceil(contentSize.height);
        const fullWidth = Math.ceil(contentSize.width);

        console.log(`BG: Page Metrics - Full: ${fullWidth}x${fullHeight}, Viewport: ${clientWidth}x${clientHeight}`);

        // Scale dimensions for high-res capture if needed (useful for stitching canvas size)
        // We'll use these values later when creating the canvas for stitching
        // const captureWidth = Math.round(clientWidth * devicePixelRatio);
        // const captureHeight = Math.round(clientHeight * devicePixelRatio);

        if (fullHeight <= clientHeight && fullWidth <= clientWidth) {
            console.log("BG: Page fits within viewport, capturing as visible tab (using debugger).");
            const result = await sendDebuggerCommand(targetId, "Page.captureScreenshot", { format: "png", quality: quality, captureBeyondViewport: true });
            return `data:image/png;base64,${result.data}`;
        }

        // --- Scrolling and Capturing Viewports ---
        const captures = [];
        const scrollStep = clientHeight;
        let currentY = 0;
        const maxScrollY = fullHeight - clientHeight;

        // Set device metrics override once, using actual devicePixelRatio
        await sendDebuggerCommand(targetId, "Emulation.setDeviceMetricsOverride", {
            mobile: false,
            width: Math.round(clientWidth), // Ensure integer values
            height: Math.round(clientHeight), // Ensure integer values
            deviceScaleFactor: devicePixelRatio || 1, // USE ACTUAL DPR
            screenOrientation: { type: 'portraitPrimary', angle: 0 }
        });

        while (currentY < fullHeight) {
            const scrollTargetY = Math.min(currentY, maxScrollY);
            console.log(`BG: Scrolling to Y=${scrollTargetY}, Capturing viewport...`);

            await sendDebuggerCommand(targetId, "Runtime.evaluate", { expression: `window.scrollTo(0, ${scrollTargetY})`, awaitPromise: true });
            await new Promise(resolve => setTimeout(resolve, 300));

            const screenshotResult = await sendDebuggerCommand(targetId, "Page.captureScreenshot", {
                format: "png",
                quality: quality,
                captureBeyondViewport: false
            });
            // Store the scroll position at which this viewport capture was taken
            captures.push({ data: screenshotResult.data, y: scrollTargetY });

            if (scrollTargetY >= maxScrollY) break;
            currentY += scrollStep;
        }

        // Reset emulation
        await sendDebuggerCommand(targetId, "Emulation.clearDeviceMetricsOverride");
        console.log("BG: Finished capturing viewports, captured:", captures.length);

        // --- Stitching ---
        if (captures.length === 0) throw new Error("No screenshots were captured.");

        console.log("BG: Stitching images...");
        // Ensure all dimensions are integers for canvas creation
        const finalCanvasPhysicalWidth = Math.round(clientWidth * devicePixelRatio);
        const finalCanvasPhysicalHeight = Math.round(fullHeight * devicePixelRatio);
        console.log(`BG: Creating final canvas with physical dimensions: ${finalCanvasPhysicalWidth}x${finalCanvasPhysicalHeight}`);
        const finalCanvas = new OffscreenCanvas(finalCanvasPhysicalWidth, finalCanvasPhysicalHeight);
        const ctx = finalCanvas.getContext('2d');
        if (!ctx) throw new Error("Failed to get OffscreenCanvas context.");
        // Optional: Fill background if needed, though shouldn't be necessary with full draw
        // ctx.fillStyle = 'white';
        // ctx.fillRect(0, 0, finalCanvasPhysicalWidth, finalCanvasPhysicalHeight);

        let stitchedPhysicalHeight = 0;
        for (let i = 0; i < captures.length; i++) {
            const capture = captures[i];
            try {
                // Convert base64 to Blob directly without using fetch (to avoid CSP issues)
                const imgBlob = base64ToBlob(capture.data, 'image/png');
                const imgBitmap = await createImageBitmap(imgBlob);

                const sourcePixelWidth = imgBitmap.width;
                const sourcePixelHeight = imgBitmap.height; // Height of this capture (should roughly be clientHeight * dpr)

                // Destination Y position on the final canvas (physical pixels)
                const destinationPixelY = Math.round(capture.y * devicePixelRatio);

                // Calculate the maximum physical height we *should* draw for this segment
                // It's limited by the remaining space on the final canvas.
                const remainingCanvasPhysicalHeight = finalCanvasPhysicalHeight - destinationPixelY;

                // The actual height to draw from the source bitmap
                // is the minimum of the source bitmap's height and the remaining canvas space.
                const drawPixelHeight = Math.min(sourcePixelHeight, remainingCanvasPhysicalHeight);

                // The source Y within the bitmap always starts at 0, as we captured the viewport
                const sourcePixelY = 0;

                if (drawPixelHeight > 0) {
                    console.log(`BG: Drawing part ${i+1}: srcY(px)=${sourcePixelY}, srcH(px)=${drawPixelHeight}, destY(px)=${destinationPixelY}, destH(px)=${drawPixelHeight}`);

                    // Draw the calculated portion
                    ctx.drawImage(imgBitmap,
                        0, sourcePixelY,             // Source rect (start at top of capture)
                        sourcePixelWidth, drawPixelHeight, // Use calculated height
                        0, destinationPixelY,        // Destination rect
                        finalCanvasPhysicalWidth, drawPixelHeight // Use calculated height
                    );
                    stitchedPhysicalHeight = Math.max(stitchedPhysicalHeight, destinationPixelY + drawPixelHeight);
                } else {
                    console.log(`BG: Skipping drawing part ${i+1} as calculated drawPixelHeight is <= 0.`);
                }

                imgBitmap.close();

            } catch (bitmapError) {
                console.error(`BG: Error processing capture part ${i+1}:`, bitmapError);
            }
        }
        console.log(`BG: Stitching complete. Final physical canvas height: ${finalCanvasPhysicalHeight}, Stitched physical content height: ${stitchedPhysicalHeight}`);

        // Convert final canvas to PNG Data URL with maximum quality
        const finalBlob = await finalCanvas.convertToBlob({
            type: 'image/png',
            quality: 1.0 // Maximum quality for PNG
        });
        const finalDataUrl = await new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onloadend = () => resolve(reader.result);
            reader.onerror = reject;
            reader.readAsDataURL(finalBlob);
        });

        return finalDataUrl;

    } catch (error) {
        console.error("BG: Error during full page capture process:", error);
        throw error;
    } finally {
        if (attached) {
            console.log("BG: Detaching debugger...");
            await chrome.debugger.detach(targetId).catch(e => console.warn("BG: Error detaching debugger:", e));
        }
    }
}

// --- END: Added Helper Functions for Full Page Capture ---

// --- START: Offscreen Document Management ---
const OFFSCREEN_DOCUMENT_PATH = 'offscreen.html';

async function hasOffscreenDocument(path = OFFSCREEN_DOCUMENT_PATH) {
    const offscreenUrl = chrome.runtime.getURL(path);
    const contexts = await chrome.runtime.getContexts({
        contextTypes: ['OFFSCREEN_DOCUMENT'],
        documentUrls: [offscreenUrl]
    });
    return contexts.length > 0;
}

async function createOffscreenDocument(path = OFFSCREEN_DOCUMENT_PATH) {
    if (await hasOffscreenDocument(path)) {
        console.log("BG: Offscreen document already exists.");
        return;
    }
    console.log("BG: Creating offscreen document...");
    await chrome.offscreen.createDocument({
        url: path,
        reasons: ['DOM_PARSER', 'BLOB'],
        justification: 'Processing API encryption and other secure operations',
    });
    console.log("BG: Offscreen document created.");
}

// --- START: Google Docs Integration ---
/**
 * Gets an OAuth2 token for Google Docs API
 * @param {Array} scopes - The scopes to request
 * @returns {Promise<string>} A promise that resolves to the auth token
 */
async function getAuthToken(scopes) {
    return new Promise((resolve, reject) => {
        chrome.identity.getAuthToken({ interactive: true, scopes: scopes }, (token) => {
            if (chrome.runtime.lastError) {
                console.error("BG: Error getting auth token:", chrome.runtime.lastError);
                reject(chrome.runtime.lastError);
            } else {
                console.log("BG: Successfully obtained auth token");
                resolve(token);
            }
        });
    });
}

/**
 * Revokes the current OAuth2 token
 * @param {string} token - The token to revoke
 * @returns {Promise<void>} A promise that resolves when the token is revoked
 */
async function revokeAuthToken(token) {
    return new Promise((resolve, reject) => {
        chrome.identity.removeCachedAuthToken({ token: token }, () => {
            if (chrome.runtime.lastError) {
                console.error("BG: Error removing cached auth token:", chrome.runtime.lastError);
                reject(chrome.runtime.lastError);
            } else {
                console.log("BG: Successfully removed cached auth token");

                // Also revoke on Google's servers
                fetch(`https://accounts.google.com/o/oauth2/revoke?token=${token}`)
                    .then(response => {
                        if (response.ok) {
                            console.log("BG: Successfully revoked auth token on server");
                            resolve();
                        } else {
                            console.error("BG: Error revoking auth token on server:", response.status);
                            reject(new Error(`Failed to revoke token: ${response.status}`));
                        }
                    })
                    .catch(error => {
                        console.error("BG: Error revoking auth token on server:", error);
                        reject(error);
                    });
            }
        });
    });
}
// --- END: Google Docs Integration ---

/**
 * Gets user profile information from Google OAuth
 * @param {string} token - OAuth token
 * @returns {Promise<Object>} User profile data
 */
async function getUserProfile(token) {
    try {
        const response = await fetch('https://www.googleapis.com/oauth2/v2/userinfo', {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Accept': 'application/json'
            }
        });

        if (!response.ok) {
            const errorText = await response.text();
            console.error("BG: Userinfo API error response:", errorText);

            if (response.status === 401) {
                throw new Error('Authentication token expired or invalid');
            }
            throw new Error(`Failed to get user profile: ${response.status} - ${errorText}`);
        }

        const userInfo = await response.json();
        console.log("BG: Successfully retrieved user profile");

        return {
            email: userInfo.email,
            name: userInfo.name,
            picture: userInfo.picture,
            id: userInfo.id
        };
    } catch (error) {
        console.error("BG: Error getting user profile:", error);
        throw error;
    }
}

/**
 * Gets user authentication token and profile for user info only
 * @returns {Promise<Object>} User authentication data
 */
async function authenticateUser() {
    try {
        console.log("BG: Starting user authentication process...");

        // First, clear any cached tokens to ensure we get a fresh one
        await new Promise((resolve) => {
            chrome.identity.clearAllCachedAuthTokens(() => {
                console.log("BG: Cleared cached auth tokens");
                resolve();
            });
        });

        // Try to get token with userinfo scopes first
        let token;
        try {
            console.log("BG: Attempting to get auth token with userinfo scopes...");
            token = await new Promise((resolve, reject) => {
                chrome.identity.getAuthToken({
                    interactive: true,
                    scopes: ['https://www.googleapis.com/auth/userinfo.email', 'https://www.googleapis.com/auth/userinfo.profile']
                }, (token) => {
                    if (chrome.runtime.lastError) {
                        console.error("BG: Error getting user auth token:", chrome.runtime.lastError);
                        reject(new Error(chrome.runtime.lastError.message || 'Failed to get auth token'));
                    } else if (!token) {
                        console.error("BG: No token returned");
                        reject(new Error('No authentication token received'));
                    } else {
                        console.log("BG: Successfully got auth token with userinfo scopes");
                        resolve(token);
                    }
                });
            });
        } catch (error) {
            console.warn("BG: Failed to get token with userinfo scopes, trying fallback:", error.message);
            // Fallback: try with basic scopes that we know work
            token = await new Promise((resolve, reject) => {
                chrome.identity.getAuthToken({
                    interactive: true
                }, (token) => {
                    if (chrome.runtime.lastError) {
                        console.error("BG: Error getting fallback auth token:", chrome.runtime.lastError);
                        reject(new Error(chrome.runtime.lastError.message || 'Failed to get auth token'));
                    } else if (!token) {
                        console.error("BG: No fallback token returned");
                        reject(new Error('No authentication token received'));
                    } else {
                        console.log("BG: Successfully got fallback auth token");
                        resolve(token);
                    }
                });
            });
        }

        // Validate token by testing it with userinfo API
        const userProfile = await getUserProfile(token);

        // Store user authentication state
        await chrome.storage.local.set({
            userAuthenticated: true,
            userProfile: userProfile,
            userAuthToken: token,
            userAuthTime: Date.now()
        });

        return {
            success: true,
            userProfile: userProfile
        };
    } catch (error) {
        console.error("BG: User authentication failed:", error);

        // Clear any stored user data on failure
        await chrome.storage.local.remove(['userAuthenticated', 'userProfile', 'userAuthToken', 'userAuthTime']);

        throw error;
    }
}

/**
 * Signs out the user and clears user authentication data
 * @returns {Promise<void>}
 */
async function signOutUser() {
    try {
        // Get stored user token
        const result = await chrome.storage.local.get(['userAuthToken']);
        const userToken = result.userAuthToken;

        // Remove cached token
        if (userToken) {
            chrome.identity.removeCachedAuthToken({ token: userToken }, () => {
                // Token removed from cache
            });

            // Revoke token on Google's servers
            try {
                await fetch(`https://oauth2.googleapis.com/revoke?token=${userToken}`, { method: 'POST' });
            } catch (error) {
                console.warn("BG: Failed to revoke user token on server:", error);
            }
        }

        // Clear all user authentication data
        await chrome.storage.local.remove(['userAuthenticated', 'userProfile', 'userAuthToken', 'userAuthTime']);
    } catch (error) {
        console.error("BG: Error during user sign out:", error);
        throw error;
    }
}

// Security audit logging
let securityAuditLog = [];
const MAX_AUDIT_ENTRIES = 1000;

/**
 * Logs security events for audit purposes
 * @param {string} event - Event type
 * @param {Object} details - Event details
 */
function logSecurityEvent(event, details) {
    const logEntry = {
        timestamp: new Date().toISOString(),
        event: event,
        details: details,
        extensionId: chrome.runtime.id
    };

    securityAuditLog.push(logEntry);

    // Limit log size
    if (securityAuditLog.length > MAX_AUDIT_ENTRIES) {
        securityAuditLog = securityAuditLog.slice(-MAX_AUDIT_ENTRIES);
    }

    console.log('BG Security: Audit event logged:', event, details);
}

/**
 * Processes Google AI requests
 * @param {string} model - The model to use
 * @param {Object} requestBody - The request body
 * @param {Function} sendResponse - The response callback
 */
function processGoogleAiRequest(model, requestBody, sendResponse) {
    // Log security event
    logSecurityEvent('api_request_received', {
        model: model,
        hasRequestBody: !!requestBody,
        timestamp: new Date().toISOString()
    });

    // Validate the request
    if (!model) {
        console.error(`BG: No model specified in Google AI request`);
        logSecurityEvent('api_request_validation_failed', {
            reason: 'No model specified',
            timestamp: new Date().toISOString()
        });
        sendResponse({
            success: false,
            error: 'No model specified in request'
        });
        return;
    }

    secureApiCall('googleAi', requestBody, { model: model })
        .then(result => {
            console.log(`BG: Google AI request successful:`, result);
            logSecurityEvent('api_request_success', {
                model: model,
                timestamp: new Date().toISOString()
            });
            sendResponse(result);
        })
        .catch(error => {
            console.error(`BG: Google AI request failed:`, error);
            logSecurityEvent('api_request_failed', {
                model: model,
                error: error.message,
                timestamp: new Date().toISOString()
            });
            sendResponse({
                success: false,
                error: error.message || 'Google AI request failed'
            });
        });
}

// --- Message Listener for Google Docs Integration ---
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    console.log('BG: Received message:', message.action);

    // Handle Google Docs integration
    if (message.action === 'getAuthToken') {
        getAuthToken(message.scopes)
            .then(token => {
                sendResponse({ success: true, token: token });
            })
            .catch(error => {
                sendResponse({ success: false, error: error.message || 'Failed to get auth token' });
            });
        return true; // Keep the message channel open for the async response
    }

    // Handle user authentication
    if (message.action === 'authenticateUser') {
        console.log('BG: Processing authenticateUser request...');
        authenticateUser()
            .then(result => {
                console.log('BG: Authentication successful, sending response:', result);
                sendResponse(result);
            })
            .catch(error => {
                console.error('BG: Authentication failed:', error);
                sendResponse({ success: false, error: error.message || 'User authentication failed' });
            });
        return true;
    }

    // Handle Google AI API requests (legacy support)
    if (message.action === 'googleAiRequest') {
        const { model, requestBody } = message;
        console.log(`BG: Received Google AI request with model: ${model}, requestBody:`, requestBody);

        // Check if universal AI configuration exists with non-Google provider
        chrome.storage.local.get(['universalAiConfig']).then(result => {
            const universalConfig = result.universalAiConfig;

            if (universalConfig && universalConfig.apiKey && universalConfig.providerConfig) {
                console.log('BG: Universal AI configuration detected, provider:', universalConfig.providerId);

                // If it's not a Google provider, reject the Google AI request
                if (universalConfig.providerId !== 'google') {
                    console.log('BG: Non-Google provider detected (' + universalConfig.providerId + '), rejecting Google AI request');
                    logSecurityEvent('google_ai_request_blocked', {
                        reason: 'Non-Google provider configured',
                        configuredProvider: universalConfig.providerId,
                        timestamp: new Date().toISOString()
                    });
                    sendResponse({
                        success: false,
                        error: `Google AI request blocked - ${universalConfig.providerConfig.displayName} is configured instead`
                    });
                    return;
                }
            }

            // Continue with Google AI request processing
            processGoogleAiRequest(model, requestBody, sendResponse);
        }).catch(configError => {
            console.warn('BG: Error checking universal AI config:', configError);
            // Continue with Google AI request processing
            processGoogleAiRequest(model, requestBody, sendResponse);
        });

        return true;
    }

    // Handle Universal AI API requests
    if (message.action === 'universalAiRequest') {
        const { url, options } = message;
        console.log(`BG: Received universal AI request to: ${url}`);

        // Log security event
        logSecurityEvent('universal_ai_request_received', {
            url: url,
            method: options?.method || 'POST',
            timestamp: new Date().toISOString()
        });

        // Make the request using fetch
        fetch(url, options)
            .then(async response => {
                if (!response.ok) {
                    const errorText = await response.text();
                    let errorMessage = `HTTP ${response.status}: ${errorText}`;

                    // Enhanced error handling for common API issues
                    if (response.status === 429) {
                        try {
                            const errorData = JSON.parse(errorText);
                            if (errorData.error && errorData.error.code === 'insufficient_quota') {
                                errorMessage = `HTTP ${response.status}: OpenAI quota exceeded. Please check your billing at https://platform.openai.com/account/billing`;
                            } else {
                                errorMessage = `HTTP ${response.status}: Rate limit exceeded. Please wait before trying again.`;
                            }
                        } catch (parseError) {
                            errorMessage = `HTTP ${response.status}: Rate limit or quota exceeded. Please check your API usage and billing.`;
                        }
                    } else if (response.status === 404 && errorText.includes('model')) {
                        errorMessage = `HTTP ${response.status}: Model not found or not accessible. Please check your OpenAI plan and try using 'gpt-3.5-turbo' instead.`;
                    }

                    throw new Error(errorMessage);
                }
                const data = await response.json();
                console.log(`BG: Universal AI request successful`);
                logSecurityEvent('universal_ai_request_success', {
                    url: url,
                    timestamp: new Date().toISOString()
                });
                sendResponse({
                    success: true,
                    data: data
                });
            })
            .catch(error => {
                console.error(`BG: Universal AI request failed:`, error);
                logSecurityEvent('universal_ai_request_failed', {
                    url: url,
                    error: error.message,
                    timestamp: new Date().toISOString()
                });
                sendResponse({
                    success: false,
                    error: error.message || 'Universal AI request failed'
                });
            });
        return true; // Keep the message channel open for the async response
    }

    // Handle API Key Validation requests
    if (message.action === 'validateApiKey') {
        const { providerId, url, method, headers } = message;
        console.log(`BG: Validating API key for provider: ${providerId}`);

        // Log security event
        logSecurityEvent('api_key_validation_started', {
            providerId: providerId,
            url: url,
            timestamp: new Date().toISOString()
        });

        // Prepare request options
        const requestOptions = {
            method: method || 'GET',
            headers: headers || {}
        };

        // For Anthropic, we need to send a minimal test message
        if (providerId === 'anthropic' && method === 'POST') {
            requestOptions.body = JSON.stringify({
                model: 'claude-3-haiku-20240307',
                max_tokens: 10,
                messages: [{ role: 'user', content: 'Hi' }]
            });
        }

        // Make the validation request
        fetch(url, requestOptions)
            .then(async response => {
                const isSuccess = response.ok;
                let responseData = null;
                let errorMessage = null;

                try {
                    responseData = await response.json();
                } catch (e) {
                    // Some APIs might not return JSON
                    if (!isSuccess) {
                        errorMessage = await response.text();
                    }
                }

                if (isSuccess) {
                    console.log(`BG: API key validation successful for ${providerId}`);
                    logSecurityEvent('api_key_validation_success', {
                        providerId: providerId,
                        timestamp: new Date().toISOString()
                    });

                    // Extract models list if available
                    let models = [];
                    if (providerId === 'openai' && responseData?.data) {
                        models = responseData.data.map(model => model.id);
                    } else if (providerId === 'google' && responseData?.models) {
                        models = responseData.models.map(model => model.name);
                    }

                    sendResponse({
                        success: true,
                        models: models,
                        details: responseData
                    });
                } else {
                    console.warn(`BG: API key validation failed for ${providerId}:`, errorMessage || responseData);
                    logSecurityEvent('api_key_validation_failed', {
                        providerId: providerId,
                        error: errorMessage || 'Validation failed',
                        timestamp: new Date().toISOString()
                    });
                    sendResponse({
                        success: false,
                        error: errorMessage || responseData?.error?.message || 'API key validation failed',
                        details: responseData
                    });
                }
            })
            .catch(error => {
                console.error(`BG: API key validation error for ${providerId}:`, error);
                logSecurityEvent('api_key_validation_error', {
                    providerId: providerId,
                    error: error.message,
                    timestamp: new Date().toISOString()
                });
                sendResponse({
                    success: false,
                    error: error.message || 'Network error during validation'
                });
            });
        return true; // Keep the message channel open for the async response
    }

    // Handle user sign out
    if (message.action === 'signOutUser') {
        signOutUser()
            .then(() => {
                sendResponse({ success: true });
            })
            .catch(error => {
                sendResponse({ success: false, error: error.message || 'Sign out failed' });
            });
        return true;
    }

    // Handle get user profile
    if (message.action === 'getUserProfile') {
        chrome.storage.local.get(['userAuthenticated', 'userProfile'])
            .then(result => {
                if (result.userAuthenticated && result.userProfile) {
                    sendResponse({ success: true, userProfile: result.userProfile });
                } else {
                    sendResponse({ success: false, error: 'User not authenticated' });
                }
            })
            .catch(error => {
                console.error('BG: Error getting user profile:', error);
                sendResponse({ success: false, error: 'Failed to get user profile' });
            });
        return true;
    }

    // Handle token revocation
    if (message.action === 'revokeAuthToken') {
        revokeAuthToken(message.token)
            .then(() => {
                sendResponse({ success: true });
            })
            .catch(error => {
                sendResponse({ success: false, error: error.message || 'Failed to revoke auth token' });
            });
        return true; // Keep the message channel open for the async response
    }

    // For other messages, don't handle here
    return false;
});



// --- END: Offscreen Document Management ---

// --- START: Secure API Proxy Service ---
/**
 * Secure API Proxy Service
 *
 * This service handles API calls to third-party services without exposing API keys to content scripts.
 * It acts as a secure proxy between content scripts and external APIs.
 */

// API Service Configuration
const API_SERVICES = {
    // Google Speech-to-Text API
    googleSpeech: {
        endpoint: 'https://speech.googleapis.com/v1/speech:recognize',
        keyName: 'googleSpeechApiKey',
        keyParam: 'key',
        method: 'POST'
    },
    // Azure Speech Service
    azureSpeech: {
        endpoint: 'https://{region}.stt.speech.microsoft.com/speech/recognition/conversation/cognitiveservices/v1',
        keyName: 'azureSpeechApiKey',
        keyHeader: 'Ocp-Apim-Subscription-Key',
        regionName: 'azureSpeechRegion',
        method: 'POST'
    },
    // AssemblyAI
    assemblyAi: {
        endpoint: 'https://api.assemblyai.com/v2/transcript',
        keyName: 'assemblyAiApiKey',
        keyHeader: 'Authorization',
        keyPrefix: 'Token ',
        method: 'POST'
    },
    // Google Gemini AI
    googleAi: {
        endpoint: '', // Will be constructed dynamically
        keyName: 'googleAiApiKey',
        keyParam: 'key',
        method: 'POST'
    }
};

/**
 * Makes a secure API call to a third-party service
 * @param {string} service - The service identifier (e.g., 'googleSpeech')
 * @param {Object} data - The data to send to the API
 * @param {Object} options - Additional options for the API call
 * @returns {Promise<Object>} - The API response
 */
async function secureApiCall(service, data, options = {}) {
    console.log(`BG: Making secure API call to ${service}`);

    // Get service configuration
    const serviceConfig = API_SERVICES[service];
    if (!serviceConfig) {
        throw new Error(`Unknown service: ${service}`);
    }

    try {
        let apiKey;

        // Handle Google AI differently - it uses secure storage with fallback
        if (service === 'googleAi') {
            try {
                // First try to get from regular storage (fallback method)
                const result = await chrome.storage.local.get(['googleAiApiKey']);
                apiKey = result.googleAiApiKey;

                // Also check if encrypted storage exists (for future use)
                const encryptedResult = await chrome.storage.local.get(['Stashy_encrypted_api_keys']);
                if (encryptedResult.Stashy_encrypted_api_keys && encryptedResult.Stashy_encrypted_api_keys.googleAi) {
                    console.log('BG: Found encrypted API key (will be used in future versions)');
                    logSecurityEvent('encrypted_api_key_found', {
                        service: service,
                        timestamp: new Date().toISOString()
                    });
                }

                if (!apiKey || !apiKey.trim()) {
                    logSecurityEvent('api_key_not_found', {
                        service: service,
                        timestamp: new Date().toISOString()
                    });
                    throw new Error(`Google AI API key not found. Please configure your API key in AI settings.`);
                }

                logSecurityEvent('api_key_retrieved', {
                    service: service,
                    keyLength: apiKey.length,
                    timestamp: new Date().toISOString()
                });

                console.log(`BG: Retrieved Google AI API key (length: ${apiKey.length})`);
            } catch (error) {
                console.error('BG: Error retrieving Google AI API key:', error);
                logSecurityEvent('api_key_retrieval_error', {
                    service: service,
                    error: error.message,
                    timestamp: new Date().toISOString()
                });
                throw new Error(`Failed to retrieve Google AI API key: ${error.message}`);
            }
        } else {
            // For other services, use encrypted voice API keys
            const result = await chrome.storage.local.get(['encryptedVoiceApiKeys']);
            let apiKeys = {};

            // Check if we have encrypted keys
            if (result.encryptedVoiceApiKeys) {
                try {
                    // Import the encryption module from the offscreen document
                    await createOffscreenDocument('offscreen-crypto.html');

                    // Send a message to the offscreen document to decrypt the keys
                    const decryptResponse = await chrome.runtime.sendMessage({
                        action: 'decryptApiKeys',
                        target: 'offscreen-crypto',
                        encryptedData: result.encryptedVoiceApiKeys
                    });

                    if (decryptResponse && decryptResponse.success) {
                        apiKeys = decryptResponse.decryptedData;
                        console.log(`BG: Successfully decrypted API keys for ${service}`);
                    } else {
                        console.error(`BG: Failed to decrypt API keys - no fallback available`);
                        throw new Error(`Failed to decrypt API keys for ${service}`);
                    }
                } catch (decryptError) {
                    console.error(`BG: Error decrypting API keys:`, decryptError);
                    throw new Error(`API keys could not be decrypted for ${service}. Please check your encryption setup.`);
                }
            } else {
                console.error(`BG: No encrypted API keys found for ${service}`);
                throw new Error(`No API keys found for ${service}. Please configure your API keys in the voice settings.`);
            }

            // Get the API key for this service
            apiKey = apiKeys[serviceConfig.keyName];
            if (!apiKey) {
                throw new Error(`API key not found for ${service}`);
            }
        }

        // Prepare the API endpoint
        let endpoint = serviceConfig.endpoint;

        // Handle region replacement for Azure
        if (service === 'azureSpeech' && options.region) {
            endpoint = endpoint.replace('{region}', options.region);
        }

        // Handle endpoint construction for Google AI
        if (service === 'googleAi' && options.model) {
            if (options.model === 'models') {
                // Special case for listing models
                endpoint = `https://generativelanguage.googleapis.com/v1beta/models`;
                console.log(`BG: Using models list endpoint: ${endpoint}`);
            } else {
                // The model parameter comes as "models/gemini-1.5-flash-latest:generateContent"
                // We need to extract just the model part and construct the full endpoint
                const modelPart = options.model.replace(':generateContent', '');
                endpoint = `https://generativelanguage.googleapis.com/v1beta/${modelPart}:generateContent`;
                console.log(`BG: Using generation endpoint: ${endpoint}`);
            }
        } else if (service === 'googleAi') {
            // If no model specified, this might be an error
            console.error(`BG: No model specified for Google AI request`);
            throw new Error('No model specified for Google AI request');
        }

        // Determine the HTTP method based on the request
        let httpMethod = serviceConfig.method;
        if (service === 'googleAi' && options.model === 'models') {
            // Models list endpoint should use GET
            httpMethod = 'GET';
        }

        // Prepare request options
        const requestOptions = {
            method: httpMethod,
            headers: {}
        };

        // Only add Content-Type for requests with body
        if (data && httpMethod !== 'GET') {
            requestOptions.headers['Content-Type'] = 'application/json';
        }

        // Add API key to request
        if (serviceConfig.keyParam) {
            // Add as URL parameter (e.g., Google)
            const separator = endpoint.includes('?') ? '&' : '?';
            endpoint += `${separator}${serviceConfig.keyParam}=${apiKey}`;
            console.log(`BG: Added API key as URL parameter for ${service}`);
        } else if (serviceConfig.keyHeader) {
            // Add as header (e.g., Azure, AssemblyAI)
            const keyValue = serviceConfig.keyPrefix ? `${serviceConfig.keyPrefix}${apiKey}` : apiKey;
            requestOptions.headers[serviceConfig.keyHeader] = keyValue;
            console.log(`BG: Added API key as header for ${service}`);
        }

        // Add request body only for non-GET requests
        if (data && httpMethod !== 'GET') {
            requestOptions.body = JSON.stringify(data);
        }

        // Add additional headers from options
        if (options.headers) {
            requestOptions.headers = {
                ...requestOptions.headers,
                ...options.headers
            };
        }

        // Make the API call
        console.log(`BG: Calling ${service} API at ${endpoint} with method ${httpMethod}`);
        console.log(`BG: Request options:`, requestOptions);
        const response = await fetchWithRetry(endpoint, requestOptions, 2);

        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`API call failed with status ${response.status}: ${errorText}`);
        }

        // Parse and return the response
        const responseData = await response.json();
        return {
            success: true,
            data: responseData
        };
    } catch (error) {
        console.error(`BG: Error making secure API call to ${service}:`, error);

        // Provide user-friendly error messages
        let userFriendlyError = error.message || 'Unknown error';

        if (error.message.includes('API key not found')) {
            userFriendlyError = `Please configure your ${serviceConfig.name || service} API key in voice settings`;
        } else if (error.message.includes('Failed to decrypt API keys')) {
            userFriendlyError = 'API key decryption failed. Please reconfigure your API keys';
        } else if (error.message.includes('401') || error.message.includes('Unauthorized')) {
            userFriendlyError = `Invalid API key for ${serviceConfig.name || service}. Please check your API key`;
        } else if (error.message.includes('403') || error.message.includes('Forbidden')) {
            userFriendlyError = `API key lacks required permissions for ${serviceConfig.name || service}`;
        } else if (error.message.includes('429') || error.message.includes('rate limit')) {
            userFriendlyError = `${serviceConfig.name || service} rate limit exceeded. Please try again later`;
        } else if (error.message.includes('quota')) {
            userFriendlyError = `${serviceConfig.name || service} quota exceeded. Please check your account`;
        } else if (error.message.includes('network') || error.message.includes('fetch')) {
            userFriendlyError = `Network error connecting to ${serviceConfig.name || service}. Please check your internet connection`;
        } else if (error.message.includes('timeout')) {
            userFriendlyError = `Request timeout for ${serviceConfig.name || service}. Please try again`;
        }

        return {
            success: false,
            error: userFriendlyError,
            technicalError: error.message,
            service: service
        };
    }
}

/**
 * Processes audio data for speech-to-text conversion
 * @param {string} provider - The provider to use (google, azure, assembly)
 * @param {string} audioDataUrl - The audio data as a base64 data URL
 * @param {Object} options - Additional options for the transcription
 * @returns {Promise<Object>} - The transcription result
 */
async function processAudioTranscription(provider, audioDataUrl, options = {}) {
    console.log(`BG: Processing audio transcription with ${provider}`);

    try {
        // Convert data URL to audio data
        const audioData = extractAudioDataFromDataUrl(audioDataUrl);
        if (!audioData) {
            throw new Error('Invalid audio data');
        }

        // Process based on provider
        switch (provider) {
            case 'google':
                return await processGoogleSpeechTranscription(audioData, options);
            case 'azure':
                return await processAzureSpeechTranscription(audioData, options);
            case 'assembly':
                return await processAssemblyAiTranscription(audioData, options);
            default:
                throw new Error(`Unsupported provider: ${provider}`);
        }
    } catch (error) {
        console.error(`BG: Error processing audio transcription:`, error);
        return {
            success: false,
            error: error.message || 'Unknown error'
        };
    }
}

/**
 * Extracts audio data from a data URL
 * @param {string} dataUrl - The data URL containing audio data
 * @returns {Object} - The extracted audio data
 */
function extractAudioDataFromDataUrl(dataUrl) {
    try {
        // Parse data URL
        const matches = dataUrl.match(/^data:audio\/([^;]+);base64,(.+)$/);
        if (!matches || matches.length !== 3) {
            throw new Error('Invalid audio data URL format');
        }

        const audioType = matches[1];
        const base64Data = matches[2];

        return {
            type: audioType,
            data: base64Data
        };
    } catch (error) {
        console.error('BG: Error extracting audio data:', error);
        return null;
    }
}

/**
 * Gets the optimal Google Speech model for a given language
 * @param {string} languageCode - The language code
 * @returns {string} The model name
 */
function getGoogleModelForLanguage(languageCode) {
    const langBase = languageCode.split('-')[0];

    // Use latest model for major languages
    if (['en', 'es', 'fr', 'de', 'it', 'pt', 'ru', 'ja', 'ko', 'zh'].includes(langBase)) {
        return 'latest_long';
    }

    // Use command and search model for Indic languages (better for native script)
    if (['hi', 'bn', 'gu', 'kn', 'ml', 'mr', 'ne', 'or', 'pa', 'si', 'ta', 'te', 'ur'].includes(langBase)) {
        return 'command_and_search';
    }

    // Use phone call model for Arabic and RTL languages
    if (['ar', 'fa', 'he'].includes(langBase)) {
        return 'phone_call';
    }

    // Default to latest short for other languages
    return 'latest_short';
}

/**
 * Processes audio data with Google Speech-to-Text API
 * @param {Object} audioData - The audio data
 * @param {Object} options - Additional options
 * @returns {Promise<Object>} - The transcription result
 */
async function processGoogleSpeechTranscription(audioData, options) {
    // Enhanced configuration for Google Speech API with native script support
    const config = {
        encoding: 'LINEAR16',
        sampleRateHertz: options.sampleRate || 16000,
        languageCode: options.language || 'en-US',
        enableAutomaticPunctuation: options.enablePunctuation !== false,
        profanityFilter: options.enableProfanityFilter === true,

        // Enhanced configuration for better native script output
        enableWordTimeOffsets: options.enableWordTimeOffsets || true,
        enableWordConfidence: true,
        maxAlternatives: 3,

        // Use enhanced models for better accuracy
        useEnhanced: options.useEnhancedModel || true,

        // Model selection based on language family
        model: getGoogleModelForLanguage(options.language),

        // Audio channel count
        audioChannelCount: 1
    };

    // Add alternative language codes for better recognition
    if (options.alternativeLanguageCodes && options.alternativeLanguageCodes.length > 0) {
        config.alternativeLanguageCodes = options.alternativeLanguageCodes.slice(0, 3); // Limit to 3
        console.log(`BG: Using alternative language codes for Google Speech:`, config.alternativeLanguageCodes);
    }

    // Language-specific enhancements
    if (options.languageFamily === 'indic') {
        console.log(`BG: Applying Indic language enhancements for ${options.language}`);
        config.enableSpeakerDiarization = false; // Better for single speaker Indic languages
        config.diarizationSpeakerCount = 1;
    } else if (options.languageFamily === 'cjk') {
        console.log(`BG: Applying CJK language enhancements for ${options.language}`);
        config.enableWordTimeOffsets = true; // Important for character-based languages
    } else if (options.languageFamily === 'rtl') {
        console.log(`BG: Applying RTL language enhancements for ${options.language}`);
        config.enableAutomaticPunctuation = true; // Important for RTL languages
    }

    const requestData = {
        config: config,
        audio: {
            content: audioData.data
        }
    };

    const response = await secureApiCall('googleSpeech', requestData);

    if (response.success && response.data) {
        // Process Google Speech API response
        const results = response.data.results || [];
        let transcript = '';

        results.forEach(result => {
            const alternatives = result.alternatives || [];
            if (alternatives.length > 0) {
                transcript += alternatives[0].transcript + ' ';
            }
        });

        return {
            success: true,
            transcript: transcript.trim()
        };
    }

    return response;
}

/**
 * Processes audio data with Azure Speech Service
 * @param {Object} audioData - The audio data
 * @param {Object} options - Additional options
 * @returns {Promise<Object>} - The transcription result
 */
async function processAzureSpeechTranscription(audioData, options) {
    // Get Azure region from storage
    const result = await chrome.storage.local.get(['voiceApiKeys']);
    const apiKeys = result.voiceApiKeys || {};
    const region = apiKeys.azureSpeechRegion || 'eastus';

    const requestOptions = {
        region: region,
        headers: {
            'Accept': 'application/json'
        }
    };

    // Enhanced query parameters for Azure Speech API with native script support
    const queryParams = new URLSearchParams({
        language: options.language || 'en-US',
        format: 'detailed',

        // Enhanced configuration for better native script output
        profanity: options.enableProfanityFilter ? 'masked' : 'raw',
        punctuation: options.enablePunctuation ? 'dictated' : 'none',

        // Language-specific enhancements
        outputformat: 'detailed'
    });

    // Add language-specific parameters for better native script support
    const language = options.language || 'en-US';
    const langBase = language.split('-')[0];

    if (options.languageFamily === 'indic') {
        console.log(`BG: Applying Azure Indic language enhancements for ${language}`);
        queryParams.set('wordLevelTimestamps', 'true');
        queryParams.set('diarization', 'false'); // Better for single speaker
    } else if (options.languageFamily === 'cjk') {
        console.log(`BG: Applying Azure CJK language enhancements for ${language}`);
        queryParams.set('wordLevelTimestamps', 'true');
        queryParams.set('enableDictation', 'true');
    } else if (options.languageFamily === 'rtl') {
        console.log(`BG: Applying Azure RTL language enhancements for ${language}`);
        queryParams.set('punctuation', 'dictated');
        queryParams.set('enableDictation', 'true');
    }

    // Convert base64 to blob for Azure
    const blob = base64ToBlob(audioData.data, `audio/${audioData.type}`);

    // Create FormData
    const formData = new FormData();
    formData.append('audio', blob);

    // Override requestOptions for FormData
    requestOptions.body = formData;
    delete requestOptions.headers['Content-Type']; // Let browser set this for FormData

    const response = await secureApiCall('azureSpeech', null, {
        ...requestOptions,
        queryParams: queryParams.toString()
    });

    if (response.success && response.data) {
        // Process Azure Speech API response
        return {
            success: true,
            transcript: response.data.DisplayText || response.data.RecognitionStatus
        };
    }

    return response;
}

/**
 * Gets the optimal AssemblyAI speech model for a given language
 * @param {string} languageCode - The language code
 * @returns {string} The model name
 */
function getAssemblyAiModelForLanguage(languageCode) {
    const langBase = languageCode.split('-')[0];

    // Use best model for major languages and non-Latin scripts
    if (['hi', 'bn', 'gu', 'kn', 'ml', 'mr', 'ne', 'or', 'pa', 'si', 'ta', 'te', 'ur',
         'zh', 'ja', 'ko', 'ar', 'fa', 'he', 'th', 'my', 'km', 'lo'].includes(langBase)) {
        return 'best';
    }

    // Use nano model for simple Latin-script languages (faster processing)
    if (['en', 'es', 'fr', 'de', 'it', 'pt', 'nl', 'sv', 'da', 'no', 'fi'].includes(langBase)) {
        return 'nano';
    }

    // Default to best model for other languages
    return 'best';
}

/**
 * Processes audio data with AssemblyAI
 * @param {Object} audioData - The audio data
 * @param {Object} options - Additional options
 * @returns {Promise<Object>} - The transcription result
 */
async function processAssemblyAiTranscription(audioData, options) {
    // First, upload the audio file to AssemblyAI
    const uploadResponse = await secureApiCall('assemblyAi', null, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        endpoint: 'https://api.assemblyai.com/v2/upload'
    });

    if (!uploadResponse.success || !uploadResponse.data || !uploadResponse.data.upload_url) {
        return {
            success: false,
            error: 'Failed to upload audio to AssemblyAI'
        };
    }

    // Enhanced transcription request for AssemblyAI with native script support
    const language = options.language || 'en';
    const langBase = language.split('-')[0];

    const transcriptionRequest = {
        audio_url: uploadResponse.data.upload_url,
        language_code: langBase, // AssemblyAI uses base language codes
        punctuate: options.enablePunctuation !== false,
        format_text: options.enableAutomaticCapitalization !== false,

        // Enhanced configuration for better native script output
        word_boost: [], // Can be populated with domain-specific words
        boost_param: 'default',
        filter_profanity: options.enableProfanityFilter === true,

        // Language-specific enhancements
        dual_channel: false,
        speech_model: getAssemblyAiModelForLanguage(language),

        // Enhanced features for better accuracy
        auto_highlights: false, // Disable to focus on transcription accuracy
        summarization: false,
        sentiment_analysis: false,
        entity_detection: false
    };

    // Add language-specific configurations
    if (options.languageFamily === 'indic') {
        console.log(`BG: Applying AssemblyAI Indic language enhancements for ${language}`);
        transcriptionRequest.speech_model = 'best'; // Use best model for Indic languages
        transcriptionRequest.language_detection = false; // Disable auto-detection for better accuracy
    } else if (options.languageFamily === 'cjk') {
        console.log(`BG: Applying AssemblyAI CJK language enhancements for ${language}`);
        transcriptionRequest.speech_model = 'best';
        transcriptionRequest.punctuate = true; // Important for CJK languages
    } else if (options.languageFamily === 'rtl') {
        console.log(`BG: Applying AssemblyAI RTL language enhancements for ${language}`);
        transcriptionRequest.speech_model = 'best';
        transcriptionRequest.format_text = true; // Important for RTL languages
    }

    const transcriptionResponse = await secureApiCall('assemblyAi', transcriptionRequest);

    if (!transcriptionResponse.success || !transcriptionResponse.data || !transcriptionResponse.data.id) {
        return {
            success: false,
            error: 'Failed to request transcription from AssemblyAI'
        };
    }

    // Poll for transcription result
    const transcriptionId = transcriptionResponse.data.id;
    let result = null;
    let attempts = 0;
    const maxAttempts = 30; // Maximum polling attempts

    while (attempts < maxAttempts) {
        attempts++;

        const pollResponse = await secureApiCall('assemblyAi', null, {
            method: 'GET',
            endpoint: `https://api.assemblyai.com/v2/transcript/${transcriptionId}`
        });

        if (!pollResponse.success || !pollResponse.data) {
            await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second
            continue;
        }

        if (pollResponse.data.status === 'completed') {
            result = {
                success: true,
                transcript: pollResponse.data.text || ''
            };
            break;
        } else if (pollResponse.data.status === 'error') {
            result = {
                success: false,
                error: pollResponse.data.error || 'Transcription failed'
            };
            break;
        }

        // Wait before polling again
        await new Promise(resolve => setTimeout(resolve, 1000));
    }

    if (!result) {
        result = {
            success: false,
            error: 'Transcription timed out'
        };
    }

    return result;
}
// --- END: Secure API Proxy Service ---

// --- Constants ---
const DRIVE_API_BASE_URL = 'https://www.googleapis.com/drive/v3';
const DRIVE_API_UPLOAD_URL = 'https://www.googleapis.com/upload/drive/v3';
const STORAGE_KEY_PREFIX = 'Stashy_note_';
const STATE_KEY_PREFIX = 'Stashy_state_'; // Used in popup/content
const HIGHLIGHT_KEY_PREFIX = 'Stashy_highlights_'; // Used in popup/content
const SYNC_LOCK_KEY = 'Stashy_sync_lock'; // For session storage lock
const DRIVE_START_PAGE_TOKEN_KEY = 'Stashy_drive_start_page_token'; // For storing Drive changes API token

// --- Notebook/Folder Constants ---
const NOTEBOOKS_STORAGE_KEY = 'Stashy_notebooks'; // Local storage key for notebook list
const NOTEBOOK_METADATA_FILENAME = 'Stashy_notebooks_metadata.json'; // Filename on Google Drive

// --- Screenshot Constants ---
const DRIVE_FOLDER_MIME_TYPE = 'application/vnd.google-apps.folder';
const SCREENSHOTS_FOLDER_NAME = 'Stashy Screenshots'; // Optional dedicated folder

// --- Context Menu IDs ---
const CONTEXT_MENU_ID_PARENT_HIGHLIGHT = "StashyHighlightParent"; // Parent Menu
const CONTEXT_MENU_ID_NOTE_FROM_SELECTION = "StashyNoteFromSelection";
// Add IDs for new style menu items
const CONTEXT_MENU_ID_HIGHLIGHT_UNDERLINE = "StashyHighlightStyle-underline";
const CONTEXT_MENU_ID_HIGHLIGHT_WAVY = "StashyHighlightStyle-wavy";
const CONTEXT_MENU_ID_HIGHLIGHT_BORDER = "StashyHighlightStyle-border-thick";
const CONTEXT_MENU_ID_HIGHLIGHT_STRIKETHROUGH = "StashyHighlightStyle-strikethrough"; // <-- Add ID for Strikethrough
const CONTEXT_MENU_ID_HIGHLIGHT_WITH_NOTE = "StashyHighlightWithNote"; // <-- Add ID for Highlight with Note
// Add IDs for new snippet highlight types
const CONTEXT_MENU_ID_HIGHLIGHT_REVIEW_PRO = "StashyHighlightReviewPro"; // <-- Add ID for Review Pro Snippet
const CONTEXT_MENU_ID_HIGHLIGHT_REVIEW_CON = "StashyHighlightReviewCon"; // <-- Add ID for Review Con Snippet
const CONTEXT_MENU_ID_HIGHLIGHT_SPEC = "StashyHighlightSpec"; // <-- Add ID for Spec Snippet
// Add IDs for new snippet highlight with note types
const CONTEXT_MENU_ID_HIGHLIGHT_REVIEW_PRO_WITH_NOTE = "StashyHighlightReviewProWithNote"; // <-- Add ID for Review Pro Snippet with Note
const CONTEXT_MENU_ID_HIGHLIGHT_REVIEW_CON_WITH_NOTE = "StashyHighlightReviewConWithNote"; // <-- Add ID for Review Con Snippet with Note
const CONTEXT_MENU_ID_HIGHLIGHT_SPEC_WITH_NOTE = "StashyHighlightSpecWithNote"; // <-- Add ID for Spec Snippet with Note
// --- End Constants ---

// --- Highlight Color Definitions ---
const HIGHLIGHT_COLORS_BG = {
    yellow: { title: "Yellow", color: 'yellow' },
    pink:   { title: "Pink", color: 'pink' },
    blue:   { title: "Blue", color: 'blue' },
    green:  { title: "Green", color: 'green' },
    purple: { title: "Purple", color: 'purple' },
};
const DEFAULT_HIGHLIGHT_COLOR_BG = 'yellow';
// --- End Highlight Color Definitions ---


// --- State ---
let currentToken = null; // Cache token temporarily
let tokenExpiryTime = 0; // Timestamp when the token expires
let screenshotsFolderId = null; // Cache screenshots folder ID
let currentStartPageToken = null; // Cache startPageToken for Drive changes API
// --- End State ---

// --- Helper Functions ---

/** Validates if a message sender is trusted
 * @param {object} sender - The sender object from chrome.runtime.onMessage
 * @param {boolean} requireContentScript - Whether the sender must be a content script (has tab property)
 * @returns {boolean} - Whether the sender is trusted
 */
function validateMessageSender(sender, requireContentScript = false) {
    // Check if the sender is from our extension
    if (sender.id !== chrome.runtime.id) {
        console.warn("BG: Rejected message from external extension:", sender.id);
        return false;
    }

    // If we require a content script, check for tab property
    if (requireContentScript && !sender.tab) {
        console.warn("BG: Rejected message - expected content script but sender has no tab:", sender);
        return false;
    }

    // Check for origin if it's a web page content script
    if (sender.tab && sender.tab.url) {
        // Validate URL is from an expected protocol (not javascript: or data:)
        try {
            const url = new URL(sender.tab.url);
            if (!['http:', 'https:'].includes(url.protocol)) {
                console.warn("BG: Rejected message from content script with suspicious protocol:", url.protocol);
                return false;
            }
        } catch (e) {
            console.warn("BG: Rejected message with invalid URL:", sender.tab.url);
            return false;
        }
    }

    return true;
}

/** Broadcasts a message to all open popup instances */
function notifyPopups(action, data) {
    chrome.runtime.sendMessage({ action: action, ...data })
        .catch(() => { /* Ignore errors if no popup is open */ });
}

/** Updates sync status in storage and notifies popups */
function updateAndNotifyStatus(status, details = null) {
    chrome.storage.local.get('lastSyncStatus', currentResult => {
        const currentStatus = currentResult.lastSyncStatus;
        // Avoid interrupting 'syncing' unless it's finished (error/synced/disconnected)
        if (currentStatus === 'syncing' && !['error', 'synced', 'disconnected'].includes(status)) {
             console.log(`BG: Deferring status update from '${currentStatus}' to '${status}' as sync is in progress.`);
             // Notify the current 'syncing' state persists
             notifyPopups('updateSyncStatus', { status: currentStatus, details: details });
             return;
         }

        const statusMessage = details ? `${status} (${details})` : status;
        console.log(`BG: Updating sync status to: ${statusMessage}`);
        chrome.storage.local.set({ lastSyncStatus: status }); // Store the status
        notifyPopups('updateSyncStatus', { status: status, details: details }); // Broadcast
    });
}

// --- Locking Mechanism ---
async function acquireSyncLock() {
    try {
        const lock = await chrome.storage.session.get(SYNC_LOCK_KEY);
        const now = Date.now();
        const lockTimeout = 5 * 60 * 1000; // 5 minutes

        if (lock[SYNC_LOCK_KEY] && (now - lock[SYNC_LOCK_KEY] < lockTimeout)) {
            console.log(`BG: Sync lock held (timestamp: ${lock[SYNC_LOCK_KEY]}). Skipping.`);
            return false; // Could not acquire lock
        }
        await chrome.storage.session.set({ [SYNC_LOCK_KEY]: now });
        console.log("BG: Sync lock acquired.");
        return true;
    } catch (e) {
        console.error("BG: Error acquiring sync lock:", e);
        return false;
    }
}

async function releaseSyncLock() {
    try {
        await chrome.storage.session.remove(SYNC_LOCK_KEY);
        console.log("BG: Sync lock released.");
    } catch (e) {
        console.error("BG: Error releasing sync lock:", e);
    }
}

// --- Authentication ---
/** Gets a valid OAuth2 token */
function getAuthToken(interactive) {
    return new Promise((resolve, reject) => {
        if (currentToken && Date.now() < tokenExpiryTime) {
            resolve(currentToken);
            return;
        }
        chrome.identity.getAuthToken({ interactive: interactive }, (token) => {
            if (chrome.runtime.lastError || !token) {
                const message = chrome.runtime.lastError?.message || "Token fetch failed or cancelled.";
                console.error("BG: getAuthToken Error:", message);
                let userMessage = `Authentication failed: ${message}.`;
                if (message.includes("user did not approve") || message.includes("cancelled")) userMessage = "Authentication cancelled or permission denied.";
                else if (message.includes("offline")) userMessage = "Authentication failed: Device is offline.";
                else if (message.includes("Invalid client ID")) userMessage = "Authentication error: Invalid configuration.";
                else if (!interactive) userMessage = "Not authenticated or requires user interaction.";
                reject(new Error(userMessage)); // Reject with user-friendly message
            } else {
                currentToken = token;
                // Set expiry slightly before actual expiry (Drive tokens usually last 1 hour)
                tokenExpiryTime = Date.now() + 3500 * 1000; // ~58 minutes
                resolve(currentToken);
            }
        });
    });
}

/** Removes cached token and attempts revoke */
function removeCachedAuthToken(tokenToRevoke) {
    return new Promise((resolve) => {
        const localToken = tokenToRevoke || currentToken;
        currentToken = null;
        tokenExpiryTime = 0;
        if (!localToken) {
            resolve();
            return;
        }
        console.log("BG: Attempting to remove cached token and revoke.");
        // Remove from Chrome's cache first
        chrome.identity.removeCachedAuthToken({ token: localToken }, () => {
            if (chrome.runtime.lastError) {
                console.warn("BG: Error removing cached token:", chrome.runtime.lastError.message);
            } else {
                console.log("BG: Removed token from Chrome cache.");
            }
            // Attempt to revoke the token with Google (best effort)
            fetch(`https://oauth2.googleapis.com/revoke?token=${localToken}`, { method: 'POST' })
                .then(response => {
                    if (response.ok) console.log("BG: Token revocation successful.");
                    else console.warn(`BG: Token revocation failed with status ${response.status}.`);
                })
                .catch(err => console.error("BG: Network error during token revocation:", err))
                .finally(() => {
                    resolve(); // Resolve regardless of revoke outcome
                });
        });
    });
}

// --- fetch with Retry and Backoff ---
async function fetchWithRetry(url, options, retries = 2, delay = 1000) {
    let lastError = null;

    for (let i = 0; i <= retries; i++) {
        try {
            const response = await fetch(url, options);

            // Don't retry on 4xx client errors unless it's 401 (auth) or 429 (rate limit)
            if (response.status >= 400 && response.status < 500 && response.status !== 401 && response.status !== 429) {
                console.log(`BG: Fetch failed with client error ${response.status} for ${url}. Not retrying.`);
                return response;
            }

            // If response is ok or it's the last retry, return it
            if (response.ok || i === retries) {
                if (i > 0) {
                    console.log(`BG: Fetch succeeded for ${url} after ${i} retries`);
                }
                return response;
            }

            console.warn(`BG: Fetch failed for ${url} with status ${response.status}. Retrying in ${delay}ms... (${retries - i} retries left)`);
            lastError = new Error(`HTTP ${response.status}: ${response.statusText}`);

        } catch (error) {
            console.warn(`BG: Fetch failed for ${url} with error: ${error.message}. Retrying in ${delay}ms... (${retries - i} retries left)`);
            lastError = error;

            if (i === retries) {
                // Provide more specific error information
                if (error.name === 'TypeError' && error.message.includes('fetch')) {
                    throw new Error(`Network error: Unable to connect to ${new URL(url).hostname}. Please check your internet connection.`);
                } else if (error.name === 'AbortError') {
                    throw new Error(`Request timeout: The request to ${new URL(url).hostname} took too long.`);
                } else {
                    throw error;
                }
            }
        }

        // Wait before retrying with exponential backoff
        await new Promise(resolve => setTimeout(resolve, delay));
        delay = Math.min(delay * 2, 10000); // Cap at 10 seconds
    }

    throw lastError || new Error("Fetch failed after multiple retries");
}

// --- Drive API Helpers ---

/** Convert storage key to safe filename */
function keyToFilename(key) {
    // Handle both notes and the single notebook metadata key
    if (key === NOTEBOOKS_STORAGE_KEY) {
         return NOTEBOOK_METADATA_FILENAME;
    }
    if (!key.startsWith(STORAGE_KEY_PREFIX)) return null;
    // Note keys: Replace non-alphanumeric (excluding _.-) with underscore, add .json
    return key.replace(/[^a-zA-Z0-9_.\-]/g, '_') + '.json';
}

// We don't convert filename back to key directly - rely on 'originalKey' in downloaded notes

/** Finds or creates Drive file ID */
async function getDriveFileId(token, filename) {
    const query = `name='${filename}' and 'appDataFolder' in parents and trashed=false`;
    // Request only 'id' field for listing efficiency
    const listUrl = `${DRIVE_API_BASE_URL}/files?q=${encodeURIComponent(query)}&spaces=appDataFolder&fields=files(id)`;

    try {
        // No need to notify status here, called within upload/sync context
        const listResponse = await fetchWithRetry(listUrl, { headers: { 'Authorization': `Bearer ${token}` } });
        if (listResponse.status === 401) { await removeCachedAuthToken(token); throw new Error("Auth token invalid (during getDriveFileId list)"); }
        if (!listResponse.ok) throw new Error(`Drive List Error: ${listResponse.status}`);
        const listResult = await listResponse.json();

        if (listResult.files && listResult.files.length > 0) {
            return listResult.files[0].id; // Found existing ID
        } else {
            // Not found, create it
            console.log(`BG: File '${filename}' not found in Drive, creating...`);
            const createUrl = `${DRIVE_API_BASE_URL}/files?fields=id`; // Request only 'id' on create
            const metadata = { name: filename, parents: ['appDataFolder'] };
            const createResponse = await fetchWithRetry(createUrl, {
                method: 'POST',
                headers: { 'Authorization': `Bearer ${token}`, 'Content-Type': 'application/json' },
                body: JSON.stringify(metadata)
            });
            if (createResponse.status === 401) { await removeCachedAuthToken(token); throw new Error("Auth token invalid (during getDriveFileId create)"); }
            if (!createResponse.ok) throw new Error(`Drive Create Error: ${createResponse.status}`);
            const createResult = await createResponse.json();
            console.log(`BG: Created file ${filename} with ID ${createResult.id}`);
            return createResult.id; // Return new ID
        }
    } catch (error) {
        console.error(`BG: Error in getDriveFileId for '${filename}':`, error.message);
        // Don't update global status here, let the caller handle it
        throw error; // Re-throw to signal failure
    }
}

/** Gets the initial startPageToken for Drive changes API */
async function getStartPageToken(token) {
    console.log("BG: Getting initial startPageToken for Drive changes API...");
    try {
        const url = `${DRIVE_API_BASE_URL}/changes/startPageToken?fields=startPageToken`;
        const response = await fetchWithRetry(url, { headers: { 'Authorization': `Bearer ${token}` } });

        if (response.status === 401) {
            await removeCachedAuthToken(token);
            throw new Error("Auth token invalid during getStartPageToken");
        }
        if (!response.ok) throw new Error(`Drive getStartPageToken Error: ${response.status}`);

        const result = await response.json();
        if (!result.startPageToken) {
            throw new Error("No startPageToken returned from Drive API");
        }

        console.log(`BG: Got startPageToken: ${result.startPageToken}`);

        // Store the token in memory and in local storage
        currentStartPageToken = result.startPageToken;
        await chrome.storage.local.set({ [DRIVE_START_PAGE_TOKEN_KEY]: result.startPageToken });

        return result.startPageToken;
    } catch (error) {
        console.error("BG: Error getting startPageToken:", error);
        throw error;
    }
}

/** Lists changes since the last sync using the stored startPageToken */
async function listChanges(token, startPageToken) {
    console.log(`BG: Listing changes since token: ${startPageToken}`);
    const changedFiles = []; // Array of { id, name, modifiedTime, removed }
    let nextPageToken = startPageToken;
    let newStartPageToken = null;

    try {
        do {
            // Include fields we need and restrict to appDataFolder space
            const fields = "nextPageToken,newStartPageToken,changes(fileId,file(id,name,modifiedTime),removed)";
            let url = `${DRIVE_API_BASE_URL}/changes?pageToken=${nextPageToken}&spaces=appDataFolder&fields=${encodeURIComponent(fields)}&pageSize=100`;

            const response = await fetchWithRetry(url, { headers: { 'Authorization': `Bearer ${token}` } });
            if (response.status === 401) {
                await removeCachedAuthToken(token);
                throw new Error("Auth token invalid during listChanges");
            }
            if (!response.ok) throw new Error(`Drive listChanges Error: ${response.status}`);

            const result = await response.json();

            // Process changes
            if (result.changes) {
                for (const change of result.changes) {
                    // Skip changes that don't have a file (shouldn't happen with our query)
                    if (change.removed) {
                        // File was removed
                        changedFiles.push({
                            id: change.fileId,
                            removed: true
                        });
                    } else if (change.file) {
                        // File was added or modified
                        changedFiles.push({
                            id: change.file.id,
                            name: change.file.name,
                            modifiedTime: change.file.modifiedTime,
                            removed: false
                        });
                    }
                }
            }

            // Update tokens for next page or save new start token
            if (result.newStartPageToken) {
                newStartPageToken = result.newStartPageToken;
            }
            nextPageToken = result.nextPageToken;
        } while (nextPageToken);

        console.log(`BG: Found ${changedFiles.length} changed files. New startPageToken: ${newStartPageToken}`);

        // Store the new startPageToken for next sync if we got one
        if (newStartPageToken) {
            currentStartPageToken = newStartPageToken;
            await chrome.storage.local.set({ [DRIVE_START_PAGE_TOKEN_KEY]: newStartPageToken });
        }

        return { changedFiles, newStartPageToken };
    } catch (error) {
        console.error("BG: Error listing changes:", error);
        throw error;
    }
}

/** Process changes to determine which files to download/update */
async function processChanges(changedFiles) {
    console.log("BG: Processing changes to determine actions...");
    const filesMap = {}; // { filename: { id, modifiedTime } } for notes
    const metadataFile = { id: null, modifiedTime: null, name: NOTEBOOK_METADATA_FILENAME }; // Track metadata file
    const removedFileIds = new Set(); // Set of removed file IDs

    try {
        // Process each changed file
        for (const file of changedFiles) {
            if (file.removed) {
                // Add to removed files set
                removedFileIds.add(file.id);
                continue;
            }

            // Skip files without name (shouldn't happen)
            if (!file.name) continue;

            // Check if it's our notebook metadata file
            if (file.name === NOTEBOOK_METADATA_FILENAME) {
                metadataFile.id = file.id;
                metadataFile.modifiedTime = file.modifiedTime;
            }
            // Check if it's a Stashy note file
            else if (file.name.endsWith('.json') && file.name.startsWith(STORAGE_KEY_PREFIX.replace(/[^a-zA-Z0-9_.\-]/g, '_'))) {
                filesMap[file.name] = { id: file.id, modifiedTime: file.modifiedTime };
            }
        }

        console.log(`BG: Processed changes: ${Object.keys(filesMap).length} notes, metadata ${metadataFile.id ? 'found' : 'not found'}, ${removedFileIds.size} removed files.`);
        return { notes: filesMap, metadata: metadataFile, removedFileIds };
    } catch (error) {
        console.error("BG: Error processing changes:", error);
        throw error;
    }
}

/** Lists all relevant JSON files in appDataFolder AND the notebook metadata file */
async function listRemoteFiles(token) {
    // First try to use the changes API if we have a startPageToken
    try {
        // Check if we have a stored startPageToken
        const storedToken = await chrome.storage.local.get(DRIVE_START_PAGE_TOKEN_KEY);
        if (storedToken[DRIVE_START_PAGE_TOKEN_KEY]) {
            currentStartPageToken = storedToken[DRIVE_START_PAGE_TOKEN_KEY];
            console.log(`BG: Using stored startPageToken: ${currentStartPageToken}`);

            // Get changes since the last sync
            const { changedFiles } = await listChanges(token, currentStartPageToken);

            // Process changes to determine which files to download/update
            return await processChanges(changedFiles);
        } else {
            console.log("BG: No stored startPageToken found, falling back to full listing");
            // Get initial startPageToken for future syncs
            await getStartPageToken(token);
            // Fall back to full listing for this sync
            return await listAllRemoteFiles(token);
        }
    } catch (error) {
        console.error("BG: Error using changes API, falling back to full listing:", error);
        // Try to get a new startPageToken for future syncs
        try {
            await getStartPageToken(token);
        } catch (e) {
            console.error("BG: Failed to get new startPageToken:", e);
        }
        // Fall back to full listing
        return await listAllRemoteFiles(token);
    }
}

/** Lists all files (fallback method when changes API fails) */
async function listAllRemoteFiles(token) {
    const filesMap = {}; // { filename: { id, modifiedTime } } for notes
    const metadataFile = { id: null, modifiedTime: null, name: NOTEBOOK_METADATA_FILENAME }; // Track metadata file
    let pageToken = null;
    // Include id, name, modifiedTime
    const fields = "nextPageToken, files(id, name, modifiedTime)";
    // Only list json files OR our specific metadata filename within the appDataFolder
    const query = `(mimeType='application/json' or name='${NOTEBOOK_METADATA_FILENAME}') and 'appDataFolder' in parents and trashed=false`;

    console.log("BG: Listing all remote notes and metadata from appDataFolder...");
    try {
        do {
            let url = `${DRIVE_API_BASE_URL}/files?spaces=appDataFolder&fields=${encodeURIComponent(fields)}&q=${encodeURIComponent(query)}&pageSize=100`;
            if (pageToken) url += `&pageToken=${pageToken}`;

            const response = await fetchWithRetry(url, { headers: { 'Authorization': `Bearer ${token}` } });
            if (response.status === 401) { await removeCachedAuthToken(token); throw new Error("Auth token invalid during list remote files"); }
            if (!response.ok) throw new Error(`Drive List Remote Error: ${response.status}`);

            const result = await response.json();
            if (result.files) {
                result.files.forEach(file => {
                     if (!file.name || !file.id) return; // Skip malformed entries

                    // Check if it's our notebook metadata file
                    if (file.name === NOTEBOOK_METADATA_FILENAME) {
                         metadataFile.id = file.id;
                         metadataFile.modifiedTime = file.modifiedTime;
                    }
                    // Check if it's a Stashy note file (heuristic based on prefix and suffix)
                    else if (file.name.endsWith('.json') && file.name.startsWith(STORAGE_KEY_PREFIX.replace(/[^a-zA-Z0-9_.\-]/g, '_'))) {
                        filesMap[file.name] = { id: file.id, modifiedTime: file.modifiedTime };
                    } else {
                        // Optional: Log if a JSON file is found but doesn't match the pattern
                        // if (file.mimeType === 'application/json') {
                        //    console.log(`BG: Skipping potentially irrelevant JSON file: ${file.name}`);
                        //}
                    }
                });
            }
            pageToken = result.nextPageToken;
        } while (pageToken);
        console.log(`BG: Found ${Object.keys(filesMap).length} remote notes. Metadata file: ${metadataFile.id ? 'Found' : 'Not Found'}.`);
        return { notes: filesMap, metadata: metadataFile, removedFileIds: new Set() }; // Return structure with both
    } catch (error) {
        console.error("BG: Error listing remote files:", error);
        throw error; // Re-throw to be caught by synchronizeNotes
    }
}

/** Downloads file content */
async function downloadFileContent(token, fileId, filename) {
    const url = `${DRIVE_API_BASE_URL}/files/${fileId}?alt=media`;
    console.log(`BG: Downloading content for ${filename} (ID: ${fileId})`);
    try {
        const response = await fetchWithRetry(url, { headers: { 'Authorization': `Bearer ${token}` } });
        if (response.status === 401) { await removeCachedAuthToken(token); throw new Error(`Auth token invalid during download of ${filename}`); }
        if (response.status === 404) throw new Error(`File not found during download: ${filename} (ID: ${fileId})`);
        if (!response.ok) throw new Error(`Drive Download Error for ${filename}: ${response.status}`);

        // Check if content-type indicates JSON, otherwise return text
        const contentType = response.headers.get("content-type");
        if (contentType && contentType.includes("application/json")) {
            // Attempt to parse JSON, handle errors
            const text = await response.text();
            try {
                const parsedData = JSON.parse(text);

                // Special handling for notebook metadata file
                if (filename === NOTEBOOK_METADATA_FILENAME) {
                    console.log(`BG: Downloaded notebook metadata, checking for _deletedIds`);

                    // Get local notebooks data to check for deleted IDs
                    const localResult = await chrome.storage.local.get([NOTEBOOKS_STORAGE_KEY]);
                    const localNotebooksData = localResult[NOTEBOOKS_STORAGE_KEY];

                    // If local data has deleted IDs, make sure they're preserved in the downloaded data
                    if (Array.isArray(localNotebooksData) &&
                        Array.isArray(localNotebooksData._deletedIds) &&
                        localNotebooksData._deletedIds.length > 0) {

                        console.log(`BG: Local data has ${localNotebooksData._deletedIds.length} deleted notebook IDs`);

                        // Ensure the downloaded data has the _deletedIds array
                        if (!Array.isArray(parsedData._deletedIds)) {
                            parsedData._deletedIds = [];
                        }

                        // Ensure the downloaded data has the _deletedNames array
                        if (!Array.isArray(parsedData._deletedNames)) {
                            parsedData._deletedNames = [];
                        }

                        // Merge the deleted IDs from local data into the downloaded data
                        for (const deletedId of localNotebooksData._deletedIds) {
                            if (!parsedData._deletedIds.includes(deletedId)) {
                                parsedData._deletedIds.push(deletedId);
                                console.log(`BG: Added deleted notebook ID ${deletedId} to downloaded data`);
                            }
                        }

                        // Merge the deleted names from local data into the downloaded data
                        if (Array.isArray(localNotebooksData._deletedNames)) {
                            for (const deletedName of localNotebooksData._deletedNames) {
                                if (!parsedData._deletedNames.includes(deletedName)) {
                                    parsedData._deletedNames.push(deletedName);
                                    console.log(`BG: Added deleted notebook name "${deletedName}" to downloaded data`);
                                }
                            }
                        }

                        // Filter out any notebooks that are in the deleted list
                        if (Array.isArray(parsedData)) {
                            const deletedIds = parsedData._deletedIds;
                            const deletedNames = parsedData._deletedNames || [];
                            const originalLength = parsedData.length;

                            // Filter the array in place
                            for (let i = parsedData.length - 1; i >= 0; i--) {
                                if (!parsedData[i]) continue;

                                // Check if notebook ID is in deleted IDs list
                                if (deletedIds.includes(parsedData[i].id)) {
                                    console.log(`BG: Removing deleted notebook ${parsedData[i].id} from downloaded data`);
                                    parsedData.splice(i, 1);
                                    continue;
                                }

                                // Check if notebook name is in deleted names list
                                if (parsedData[i].name &&
                                    deletedNames.includes(parsedData[i].name.toLowerCase())) {
                                    console.log(`BG: Removing notebook with deleted name "${parsedData[i].name}" from downloaded data`);

                                    // Also add its ID to the deleted IDs list if not already there
                                    if (!deletedIds.includes(parsedData[i].id)) {
                                        deletedIds.push(parsedData[i].id);
                                        console.log(`BG: Added ID ${parsedData[i].id} of notebook with deleted name to deletedIds`);
                                    }

                                    parsedData.splice(i, 1);
                                }
                            }

                            if (originalLength !== parsedData.length) {
                                console.log(`BG: Filtered out ${originalLength - parsedData.length} deleted notebooks from downloaded data`);
                            }
                        }
                    }
                }

                return parsedData;
            } catch (jsonError) {
                 console.error(`BG: Failed to parse JSON for ${filename} (Content-Type: ${contentType}). Content: "${text.substring(0, 100)}..."`, jsonError);
                 throw new Error(`Downloaded file ${filename} is not valid JSON, despite Content-Type.`);
            }
        } else {
             console.warn(`BG: Downloaded file ${filename} is not JSON (Content-Type: ${contentType}). Returning as text.`);
             return await response.text(); // Return as text if not JSON
        }
    } catch (error) {
        console.error(`BG: Failed download for ${filename}:`, error);
        throw error; // Re-throw
    }
}

/** Downloads and saves a note locally, requires 'originalKey' in content */
async function downloadAndSaveNote(token, fileId, filename) {
    try {
        const noteData = await downloadFileContent(token, fileId, filename);

        // Basic validation of downloaded data
        if (typeof noteData !== 'object' || noteData === null) {
            throw new Error(`Invalid data format received for note ${filename}`);
        }

        // **CRITICAL: Get the original storage key from the downloaded data**
        const originalKey = noteData.originalKey;

        if (!originalKey || !originalKey.startsWith(STORAGE_KEY_PREFIX)) {
            console.warn(`BG: Downloaded note ${filename} missing or invalid 'originalKey'. Skipping save.`);
            throw new Error(`Missing originalKey in downloaded file ${filename}`);
        }

        // Prepare data for local storage (remove internal fields)
        const dataToSave = { ...noteData };
        delete dataToSave.originalKey; // Don't store the key within the local value itself

        // Add the fileId to the local note for future reference
        dataToSave.fileId = fileId;

        // Save to local storage
        await chrome.storage.local.set({ [originalKey]: dataToSave });
        console.log(`BG: Successfully downloaded and saved note ${filename} to key ${originalKey} with fileId ${fileId}`);

    } catch (error) {
        console.error(`BG: Failed to process download for ${filename}:`, error);
        throw error; // Re-throw to be caught by Promise.allSettled in sync
    }
}

/** Uploads NOTE content (creates or updates), adds 'originalKey' */
async function uploadNote(token, key, noteData, fileId = null) {
    const filename = keyToFilename(key);
    if (!filename) {
         console.error(`BG: Cannot generate filename for key ${key}. Skipping note upload.`);
         throw new Error(`Invalid filename for key ${key}`);
    }

    // Ensure essential data exists (add notebookId check)
    if (typeof noteData !== 'object' || noteData === null || !noteData.hasOwnProperty('text') || !noteData.hasOwnProperty('notebookId')) {
        console.error(`BG: Invalid or incomplete note data for key ${key}. Skipping upload.`);
        throw new Error(`Invalid note data for ${key}`);
    }

    // **CRITICAL: Add the original key to the data being uploaded**
    const dataToUpload = { ...noteData, originalKey: key };
    const content = JSON.stringify(dataToUpload);

    let url;
    let method;
    let operation = fileId ? "Updating" : "Creating";

    try {
        if (!fileId) {
             // If no fileId provided, get or create it first
             console.log(`BG: No fileId provided for note ${filename}, resolving...`);
             fileId = await getDriveFileId(token, filename); // Will create if needed
             if (!fileId) throw new Error(`Failed to get/create fileId for note ${filename}`);
             operation = "Uploading note to"; // Could be create or update now
        }

        // Always use PATCH for updating existing file media content
        console.log(`BG: ${operation} Drive file ${filename} (ID: ${fileId}) from key ${key}`);
        url = `${DRIVE_API_UPLOAD_URL}/files/${fileId}?uploadType=media`;
        method = 'PATCH';

        const response = await fetchWithRetry(url, {
            method: method,
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            },
            body: content
        }, 3); // Retry uploads

        if (response.status === 401) { await removeCachedAuthToken(token); throw new Error(`Auth token invalid during upload of ${filename}`); }
        // Check for 404 specifically on PATCH (though getDriveFileId should prevent this)
        if (response.status === 404 && method === 'PATCH') throw new Error(`File not found during upload update: ${filename} (ID: ${fileId})`);
        if (!response.ok) throw new Error(`Drive Upload Error for note ${filename}: ${response.status} ${await response.text()}`);

        console.log(`BG: Successfully uploaded note ${filename} (ID: ${fileId})`);

        // Store the fileId in the local note for future reference
        try {
            const localNote = await chrome.storage.local.get(key);
            if (localNote[key]) {
                localNote[key].fileId = fileId;
                await chrome.storage.local.set({ [key]: localNote[key] });
                console.log(`BG: Updated local note ${key} with fileId ${fileId}`);
            }
        } catch (storageError) {
            console.warn(`BG: Failed to update local note ${key} with fileId ${fileId}:`, storageError);
            // Don't throw error here, the upload was successful
        }

    } catch (error) {
        console.error(`BG: Failed note upload for ${filename}:`, error);
        throw error; // Re-throw
    }
}

/** Uploads METADATA (notebooks array), creates or updates file */
async function uploadMetadataFile(token, localNotebooksData, remoteMetadata) {
     const filename = NOTEBOOK_METADATA_FILENAME;

     // Create a deep copy to avoid modifying the original object
     let dataToUpload = JSON.parse(JSON.stringify(localNotebooksData));

     // Ensure _lastUpdated exists before upload
     if (!dataToUpload._lastUpdated) {
         dataToUpload._lastUpdated = Date.now();
         console.warn("BG: _lastUpdated timestamp missing in local notebook data for upload, adding one.");
     } else {
         // Always update timestamp to ensure this upload takes precedence
         dataToUpload._lastUpdated = Date.now();
     }

     // Ensure _deletedIds exists to track deleted notebooks
     if (!dataToUpload._deletedIds) {
         dataToUpload._deletedIds = [];
     }

     // Ensure _deletedNames exists to track deleted notebook names
     if (!dataToUpload._deletedNames) {
         dataToUpload._deletedNames = [];
     }

     // Log deleted notebooks for debugging
     if (dataToUpload._deletedIds && dataToUpload._deletedIds.length > 0) {
         console.log(`BG: Uploading metadata with ${dataToUpload._deletedIds.length} deleted notebook IDs: ${JSON.stringify(dataToUpload._deletedIds)}`);
     }

     if (dataToUpload._deletedNames && dataToUpload._deletedNames.length > 0) {
         console.log(`BG: Uploading metadata with ${dataToUpload._deletedNames.length} deleted notebook names: ${JSON.stringify(dataToUpload._deletedNames)}`);
     }

     // Filter out any notebooks that are in the deleted list
     if (Array.isArray(dataToUpload)) {
         const deletedIds = Array.isArray(dataToUpload._deletedIds) ? dataToUpload._deletedIds : [];
         const deletedNames = Array.isArray(dataToUpload._deletedNames) ? dataToUpload._deletedNames : [];

         if (deletedIds.length > 0 || deletedNames.length > 0) {
             const originalLength = dataToUpload.length;

             // Filter the array in place
             for (let i = dataToUpload.length - 1; i >= 0; i--) {
                 if (!dataToUpload[i]) continue;

                 // Check if notebook ID is in deleted IDs list
                 if (deletedIds.includes(dataToUpload[i].id)) {
                     console.log(`BG: Removing deleted notebook ${dataToUpload[i].id} from upload data`);
                     dataToUpload.splice(i, 1);
                     continue;
                 }

                 // Check if notebook name is in deleted names list
                 if (dataToUpload[i].name &&
                     deletedNames.includes(dataToUpload[i].name.toLowerCase())) {
                     console.log(`BG: Removing notebook with deleted name "${dataToUpload[i].name}" from upload data`);

                     // Also add its ID to the deleted IDs list if not already there
                     if (!deletedIds.includes(dataToUpload[i].id)) {
                         deletedIds.push(dataToUpload[i].id);
                         console.log(`BG: Added ID ${dataToUpload[i].id} of notebook with deleted name to deletedIds`);
                     }

                     dataToUpload.splice(i, 1);
                 }
             }

             if (originalLength !== dataToUpload.length) {
                 console.log(`BG: Filtered out ${originalLength - dataToUpload.length} deleted notebooks from upload data`);
             }
         }
     }

     const content = JSON.stringify(dataToUpload || []); // Ensure it's stringified

     try {
          let fileId = remoteMetadata?.id; // Use existing ID if found
          let operation = "Updating";

         if (!fileId) {
             console.log(`BG: No remote fileId for ${filename}, resolving...`);
             fileId = await getDriveFileId(token, filename); // Will create if doesn't exist
             if (!fileId) throw new Error(`Failed to get/create fileId for ${filename}`);
             operation = "Uploading metadata to";
         }

         console.log(`BG: ${operation} Drive file ${filename} (ID: ${fileId})`);
         const url = `${DRIVE_API_UPLOAD_URL}/files/${fileId}?uploadType=media`;
         const method = 'PATCH'; // Always update/overwrite the metadata file

         const response = await fetchWithRetry(url, {
            method: method,
            headers: { 'Authorization': `Bearer ${token}`, 'Content-Type': 'application/json' },
            body: content
         }, 3); // Retry uploads

         if (response.status === 401) { await removeCachedAuthToken(token); throw new Error(`Auth token invalid during upload of ${filename}`); }
         if (response.status === 404 && method === 'PATCH') throw new Error(`File not found during metadata upload: ${filename} (ID: ${fileId})`);
         if (!response.ok) throw new Error(`Drive Upload Error for metadata ${filename}: ${response.status} ${await response.text()}`);

         console.log(`BG: Successfully uploaded metadata ${filename} (ID: ${fileId})`);

     } catch (error) {
          console.error(`BG: Failed metadata upload for ${filename}:`, error);
          throw error; // Re-throw
     }
}

// --- Screenshot Drive Helpers ---

/** Finds or creates the dedicated Screenshots folder
 * @param {string} token - The OAuth token
 * @param {string} folderName - The name of the folder to find or create
 * @param {boolean} useAppDataFolder - Whether to use the appDataFolder (private) or My Drive (visible)
 * @returns {Promise<string>} - The folder ID
 */
async function getScreenshotsFolderId(token, folderName = SCREENSHOTS_FOLDER_NAME, useAppDataFolder = true) {
    // Don't use cached ID if folder name or visibility has changed
    if (screenshotsFolderId && folderName === SCREENSHOTS_FOLDER_NAME) {
        return screenshotsFolderId;
    }

    console.log(`BG: Searching for screenshots folder: "${folderName}" (private: ${useAppDataFolder})`);

    try {
        let query, listUrl, parentFolder;

        if (useAppDataFolder) {
            // Search in appDataFolder (private)
            parentFolder = 'appDataFolder';
            query = `name='${folderName}' and mimeType='${DRIVE_FOLDER_MIME_TYPE}' and '${parentFolder}' in parents and trashed=false`;
            listUrl = `${DRIVE_API_BASE_URL}/files?q=${encodeURIComponent(query)}&spaces=${parentFolder}&fields=files(id)`;
        } else {
            // Search in root of My Drive (visible)
            parentFolder = 'root';
            query = `name='${folderName}' and mimeType='${DRIVE_FOLDER_MIME_TYPE}' and '${parentFolder}' in parents and trashed=false`;
            listUrl = `${DRIVE_API_BASE_URL}/files?q=${encodeURIComponent(query)}&fields=files(id)`;
        }

        const listResponse = await fetchWithRetry(listUrl, { headers: { 'Authorization': `Bearer ${token}` } });
        if (listResponse.status === 401) { await removeCachedAuthToken(token); throw new Error("Auth token invalid (during getScreenshotsFolderId list)"); }
        if (!listResponse.ok) throw new Error(`Drive List Folder Error: ${listResponse.status}`);
        const listResult = await listResponse.json();

        if (listResult.files && listResult.files.length > 0) {
            const folderId = listResult.files[0].id;
            console.log(`BG: Found screenshots folder "${folderName}" with ID: ${folderId}`);

            // Only cache the ID if it's the default folder
            if (folderName === SCREENSHOTS_FOLDER_NAME) {
                screenshotsFolderId = folderId;
            }

            return folderId;
        } else {
            console.log(`BG: Screenshots folder "${folderName}" not found, creating...`);
            const createUrl = `${DRIVE_API_BASE_URL}/files?fields=id`;
            const metadata = {
                name: folderName,
                mimeType: DRIVE_FOLDER_MIME_TYPE,
                parents: [parentFolder]
            };
            const createResponse = await fetchWithRetry(createUrl, {
                method: 'POST',
                headers: { 'Authorization': `Bearer ${token}`, 'Content-Type': 'application/json' },
                body: JSON.stringify(metadata)
            });
            if (createResponse.status === 401) { await removeCachedAuthToken(token); throw new Error("Auth token invalid (during getScreenshotsFolderId create)"); }
            if (!createResponse.ok) throw new Error(`Drive Create Folder Error: ${createResponse.status}`);
            const createResult = await createResponse.json();
            const folderId = createResult.id;
            console.log(`BG: Created screenshots folder "${folderName}" with ID: ${folderId}`);

            // Only cache the ID if it's the default folder
            if (folderName === SCREENSHOTS_FOLDER_NAME) {
                screenshotsFolderId = folderId;
            }

            return folderId;
        }
    } catch (error) {
        console.error(`BG: Error finding/creating screenshots folder "${folderName}":`, error.message);
        // Only reset cache if it's the default folder
        if (folderName === SCREENSHOTS_FOLDER_NAME) {
            screenshotsFolderId = null; // Reset cache on error
        }
        throw error; // Re-throw
    }
}

/**
 * Uploads a screenshot Blob to Google Drive
 * @param {string} token - The OAuth token
 * @param {Blob} blob - The screenshot blob to upload
 * @param {string} filename - The filename for the screenshot
 * @param {string} folderName - The name of the folder to upload to (default: SCREENSHOTS_FOLDER_NAME)
 * @param {boolean} useAppDataFolder - Whether to use the appDataFolder (private) or My Drive (visible)
 * @returns {Promise<Object>} - The Drive file object with id, webViewLink, etc.
 */
async function uploadScreenshotBlob(token, blob, filename, folderName = SCREENSHOTS_FOLDER_NAME, useAppDataFolder = true) {
    if (!blob) {
        throw new Error("Screenshot Blob is invalid.");
    }
    try {
        // Get folder ID using the provided folder name and visibility setting
        const folderId = await getScreenshotsFolderId(token, folderName, useAppDataFolder);

        const metadata = {
            name: filename,
            parents: [folderId], // Put it in the dedicated folder
            mimeType: blob.type || 'image/png' // Use blob type or default
        };

        // Request links in the response (note: webViewLink may be null for appDataFolder)
        const createUrl = `${DRIVE_API_UPLOAD_URL}/files?uploadType=multipart&fields=id,webViewLink,webContentLink`;

        const form = new FormData();
        form.append('metadata', new Blob([JSON.stringify(metadata)], { type: 'application/json' }));
        form.append('file', blob);

        console.log(`BG: Uploading screenshot ${filename} to folder "${folderName}" (ID: ${folderId}, private: ${useAppDataFolder})`);
        updateAndNotifyStatus('syncing', `Uploading ${filename}...`);

        const response = await fetchWithRetry(createUrl, {
            method: 'POST',
            headers: { 'Authorization': `Bearer ${token}` },
            body: form
        }, 2); // Retry screenshot uploads

        if (response.status === 401) {
            await removeCachedAuthToken(token);
            throw new Error(`Auth token invalid during screenshot upload of ${filename}`);
        }
        if (!response.ok) throw new Error(`Drive Screenshot Upload Error for ${filename}: ${response.status} ${await response.text()}`);

        const result = await response.json();
        console.log(`BG: Successfully uploaded screenshot ${filename} (ID: ${result.id})`);

        // Add folder information to the result for reference
        result.folderName = folderName;
        result.folderId = folderId;
        result.isPrivate = useAppDataFolder;

        updateAndNotifyStatus('synced', `Uploaded ${filename}`); // Update status after successful upload
        return result; // Contains id, webViewLink, webContentLink, etc.

    } catch (error) {
        console.error(`BG: Failed screenshot upload for ${filename}:`, error);
        updateAndNotifyStatus('error', `Upload failed: ${filename}`);
        throw error; // Re-throw
    }
}
// --- End Screenshot Drive Helpers ---


// --- Local Storage Notes ---
/** Lists all local notes */
async function listLocalNotes() {
    try {
        const allItems = await chrome.storage.local.get(null);
        const localNotesMap = {}; // { key: { noteData with lastSaved } }
        for (const [key, value] of Object.entries(allItems)) {
            // Check prefix and ensure it's an object with a lastSaved property
            if (key.startsWith(STORAGE_KEY_PREFIX) && typeof value === 'object' && value && value.hasOwnProperty('lastSaved')) {
                 const noteData = { ...value }; // Shallow copy
                // Ensure lastSaved is a valid number timestamp
                noteData.lastSaved = Number(noteData.lastSaved) || 0;
                // Ensure notebookId field exists, defaulting to null
                noteData.notebookId = noteData.notebookId || null;
                localNotesMap[key] = noteData;
            }
        }
        console.log(`BG: Found ${Object.keys(localNotesMap).length} valid local notes.`);
        return localNotesMap;
    } catch (error) {
        console.error("BG: Error listing local notes:", error);
        throw error; // Propagate error
    }
}

// --- Synchronization Core Logic ---
async function synchronizeNotes() {
    if (!(await acquireSyncLock())) {
        return; // Exit if lock not acquired
    }

    console.log("BG: --- Starting Synchronization Process ---");
    updateAndNotifyStatus('syncing', 'Starting sync...');

    try {
        // --- Premium Check for Google Drive Sync ---
        // Check if user has premium access for Drive sync
        try {
            const premiumStatus = await chrome.runtime.sendMessage({
                action: 'getPremiumStatus'
            });

            if (premiumStatus && !premiumStatus.isFeatureAvailable?.('drive_sync')) {
                console.log('BG: Drive sync blocked - premium feature required');
                updateAndNotifyStatus('disconnected', 'Google Drive sync requires Stashy Pro');
                await chrome.storage.local.set({ driveSyncEnabled: false });
                await releaseSyncLock();
                return;
            }
        } catch (premiumError) {
            console.log('BG: Could not check premium status for Drive sync:', premiumError.message);
            // Continue with sync if premium check fails (fallback behavior)
        }

        const enabled = await chrome.storage.local.get('driveSyncEnabled');
        if (!enabled.driveSyncEnabled) {
            updateAndNotifyStatus('disconnected');
            console.log("BG: Drive sync disabled, skipping sync.");
            await releaseSyncLock();
            return;
        }

        const token = await getAuthToken(false); // Get non-interactive token

        // 1. Fetch Remote Data (Notes AND Metadata)
        updateAndNotifyStatus('syncing', 'Listing remote data...');
        const { notes: remoteNotesMap, metadata: remoteMetadata, removedFileIds } = await listRemoteFiles(token);

        // 2. Fetch Local Data (Notes AND Metadata)
        updateAndNotifyStatus('syncing', 'Listing local data...');
        const localNotesMap = await listLocalNotes();
        const localMetadataResult = await chrome.storage.local.get([NOTEBOOKS_STORAGE_KEY]);
        let localNotebooksData = localMetadataResult[NOTEBOOKS_STORAGE_KEY]; // Could be undefined or array
        // --- Validate and Prepare Local Notebooks Data ---
        if (!Array.isArray(localNotebooksData)) {
            if (localNotebooksData !== undefined) { // Only log if it exists but is not an array
                console.warn("BG: Local notebook data is not an array, resetting to empty.", localNotebooksData);
            }
            localNotebooksData = []; // Default to empty array if missing or invalid
            // If local data was invalid, ensure it gets timestamped for potential upload
            if (!localNotebooksData._lastUpdated) {
                 localNotebooksData._lastUpdated = Date.now();
            }
        }
        const localNotebooksTimestamp = localNotebooksData._lastUpdated || 0; // Requires _lastUpdated field
        console.log(`BG: Local notebook timestamp: ${localNotebooksTimestamp}`);
        // ------------------------------------------------

        // 3. Compare Metadata (Notebooks List) using _lastUpdated LWW
        updateAndNotifyStatus('syncing', 'Comparing metadata...');
        let uploadMetadata = false;
        let downloadMetadata = false;
        let remoteNotebooksData = null; // To store valid downloaded data
        let metadataSuccess = true; // Assume success unless error occurs
        const remoteMetadataTimestamp = remoteMetadata.modifiedTime ? new Date(remoteMetadata.modifiedTime).getTime() : 0;
        const buffer = 2000; // 2 seconds buffer

        // --- Metadata Download Attempt (if remote exists) ---
        if (remoteMetadata.id && remoteMetadataTimestamp > 0) {
            console.log("BG SYNC [Meta]: Remote metadata file exists. Attempting download for comparison...");
            try {
                const downloadedContent = await downloadFileContent(token, remoteMetadata.id, NOTEBOOK_METADATA_FILENAME);
                // *** VALIDATE DOWNLOADED METADATA ***
                if (Array.isArray(downloadedContent)) {
                    remoteNotebooksData = downloadedContent; // Store valid data
                    // Ensure it has a timestamp for comparison (add if missing, might indicate older format)
                    if (!remoteNotebooksData._lastUpdated) {
                        console.warn("BG: Remote metadata missing _lastUpdated timestamp. Using file modification time.");
                        // We'll use remoteMetadataTimestamp derived from file properties for comparison
                    }
                     console.log(`BG SYNC [Meta]: Successfully downloaded and validated remote metadata. Timestamp: ${remoteNotebooksData._lastUpdated || remoteMetadataTimestamp}`);
                } else {
                    // *** HANDLE MALFORMED REMOTE DATA ***
                    console.error("BG SYNC [Meta]: Error - Downloaded remote metadata is not an array or is malformed.", downloadedContent);
                    // Action: Prioritize local upload IF local data is valid. Otherwise, potentially reset both?
                    if (Array.isArray(localNotebooksData) && localNotebooksData.length >= 0) { // Allow empty local array as valid
                         console.warn("BG SYNC [Meta]: Remote data invalid, local data exists/is empty array. Prioritizing LOCAL upload.");
                         uploadMetadata = true; // Force upload of local valid data over corrupt remote data
                         metadataSuccess = false; // Mark metadata sync as problematic due to remote corruption
                    } else {
                         console.warn("BG SYNC [Meta]: Both remote and local metadata seem invalid or unusable. Doing nothing for metadata this cycle.");
                         // Avoid download/upload loops with bad data. Sync might fix it next time if one side gets corrected.
                         metadataSuccess = false; // Mark potential issue, but don't block note sync
                    }
                     throw new Error("Downloaded metadata is not an array or is malformed."); // Throw to mark metadata part as failed
                }
            } catch (downloadError) {
                console.error("BG: Error during metadata download for comparison:", downloadError);
                metadataSuccess = false; // Flag metadata part as failed
                // Continue with note comparison if possible
            }
        }
        // --- End Metadata Download Attempt ---

        // --- Metadata Comparison Logic (using remoteNotebooksData if valid) ---
        if (remoteNotebooksData) { // Only compare if download was successful and valid
            if (!localMetadataResult.hasOwnProperty(NOTEBOOKS_STORAGE_KEY)) { // Only exists remotely (and is valid)
                downloadMetadata = true; // Now we are sure it's valid data to download
                console.log("BG SYNC [Meta]: Valid remote exists, local doesn't. Queue Download.");
            } else { // Exists in both places
                const localTime = Number(localNotebooksData._lastUpdated) || 0;
                const remoteTime = Number(remoteNotebooksData._lastUpdated) || remoteMetadataTimestamp; // Use file time as fallback

                // Check if local has deleted notebooks that remote doesn't know about
                const localHasDeletedIds = Array.isArray(localNotebooksData._deletedIds) && localNotebooksData._deletedIds.length > 0;
                const remoteHasDeletedIds = Array.isArray(remoteNotebooksData._deletedIds) && remoteNotebooksData._deletedIds.length > 0;
                const localHasDeletedNames = Array.isArray(localNotebooksData._deletedNames) && localNotebooksData._deletedNames.length > 0;
                const remoteHasDeletedNames = Array.isArray(remoteNotebooksData._deletedNames) && remoteNotebooksData._deletedNames.length > 0;

                // If local has deleted IDs or names that remote doesn't have, prioritize upload
                if (localHasDeletedIds || localHasDeletedNames) {
                    let hasNewDeletedInfo = false;

                    // Check for new deleted IDs
                    if (localHasDeletedIds) {
                        const localDeletedIds = localNotebooksData._deletedIds;
                        const remoteDeletedIds = remoteHasDeletedIds ? remoteNotebooksData._deletedIds : [];

                        // Check if there are any local deleted IDs that aren't in remote
                        if (localDeletedIds.some(id => !remoteDeletedIds.includes(id))) {
                            console.log(`BG SYNC [Meta]: Local has deleted notebook IDs that remote doesn't have.`);
                            hasNewDeletedInfo = true;
                        }
                    }

                    // Check for new deleted names
                    if (localHasDeletedNames) {
                        const localDeletedNames = localNotebooksData._deletedNames;
                        const remoteDeletedNames = remoteHasDeletedNames ? remoteNotebooksData._deletedNames : [];

                        // Check if there are any local deleted names that aren't in remote
                        if (localDeletedNames.some(name => !remoteDeletedNames.includes(name))) {
                            console.log(`BG SYNC [Meta]: Local has deleted notebook names that remote doesn't have.`);
                            hasNewDeletedInfo = true;
                        }
                    }

                    if (hasNewDeletedInfo) {
                        console.log(`BG SYNC [Meta]: Local has deleted notebook information that remote doesn't have. Prioritizing upload.`);
                        uploadMetadata = true;
                        // Skip further timestamp comparison
                        console.log(`BG SYNC [Meta]: Skipping timestamp comparison due to deleted notebook information.`);
                        // Continue to next section
                        // This will prevent downloadMetadata from being set to true
                    } else if (isNaN(localTime) || isNaN(remoteTime)) {
                        console.warn(`BG SYNC [Meta]: Invalid timestamp after download. Local: ${localTime}, Remote Effective: ${remoteTime}. Prioritizing Remote -> Queueing download.`);
                        downloadMetadata = true;
                    } else if (localTime > remoteTime + buffer) {
                        uploadMetadata = true;
                        console.log(`BG SYNC [Meta]: Local timestamp (${localTime}) > Remote (${remoteTime}). Queue Upload.`);
                    } else if (remoteTime > localTime + buffer) {
                        downloadMetadata = true;
                        console.log(`BG SYNC [Meta]: Remote timestamp (${remoteTime}) > Local (${localTime}). Queue Download.`);
                    } else {
                        console.log("BG SYNC [Meta]: Timestamps within buffer. Assuming synced.");
                    }
                } else if (isNaN(localTime) || isNaN(remoteTime)) {
                    console.warn(`BG SYNC [Meta]: Invalid timestamp after download. Local: ${localTime}, Remote Effective: ${remoteTime}. Prioritizing Remote -> Queueing download.`);
                    downloadMetadata = true;
                } else if (localTime > remoteTime + buffer) {
                    uploadMetadata = true;
                    console.log(`BG SYNC [Meta]: Local timestamp (${localTime}) > Remote (${remoteTime}). Queue Upload.`);
                } else if (remoteTime > localTime + buffer) {
                    downloadMetadata = true;
                    console.log(`BG SYNC [Meta]: Remote timestamp (${remoteTime}) > Local (${localTime}). Queue Download.`);
                } else {
                    console.log("BG SYNC [Meta]: Timestamps within buffer. Assuming synced.");
                }
            }
        } else if (!remoteMetadata.id && localMetadataResult.hasOwnProperty(NOTEBOOKS_STORAGE_KEY)) {
             // Only exists locally (or remote download failed/invalid)
             // Check if local data is actually an array before deciding to upload
             if(Array.isArray(localNotebooksData)) {
                uploadMetadata = true;
                console.log("BG SYNC [Meta]: Local exists, remote doesn't (or is invalid). Queue Upload.");
             } else {
                 console.warn("BG SYNC [Meta]: Local data exists but is not an array. Skipping upload.");
                 metadataSuccess = false; // Mark as issue
             }
        }
        // --- End Metadata Comparison ---

        // 4. Compare Notes (Existing LWW Logic)
        updateAndNotifyStatus('syncing', 'Comparing notes...');
        const noteActions = { uploadNew: [], uploadUpdate: [], downloadNew: [], downloadUpdate: [], deleteLocal: [] };
        const remoteNoteFilenames = new Set(Object.keys(remoteNotesMap));
        const localNoteKeys = new Set(Object.keys(localNotesMap));

        // Check for files that were removed remotely (from changes API)
        if (removedFileIds && removedFileIds.size > 0) {
            console.log(`BG SYNC: Processing ${removedFileIds.size} files removed remotely`);

            // Create a map of fileId to key for local notes
            const localFileIdToKeyMap = new Map();

            // First, we need to find the fileIds for local notes
            // This requires downloading each note to check its fileId
            for (const key of localNoteKeys) {
                const localNote = localNotesMap[key];
                // If the note has a fileId property, use it
                if (localNote.fileId) {
                    localFileIdToKeyMap.set(localNote.fileId, key);
                }
            }

            // Now check if any local notes have fileIds that were removed remotely
            for (const fileId of removedFileIds) {
                if (localFileIdToKeyMap.has(fileId)) {
                    const key = localFileIdToKeyMap.get(fileId);
                    console.log(`BG SYNC: File with ID ${fileId} was removed remotely, queueing local deletion for key ${key}`);
                    noteActions.deleteLocal.push({ key });
                }
            }
        }

        // Iterate through local notes
        for (const key of localNoteKeys) {
            const localNote = localNotesMap[key];
            const filename = keyToFilename(key);
            if (!filename) continue;

            if (remoteNoteFilenames.has(filename)) { // Exists in both
                const remoteFile = remoteNotesMap[filename];
                const localTime = localNote.lastSaved || 0;
                const remoteTime = remoteFile.modifiedTime ? new Date(remoteFile.modifiedTime).getTime() : 0;

                if (isNaN(localTime) || isNaN(remoteTime)) {
                     console.warn(`BG SYNC [Note]: Invalid timestamp for ${filename}. Local: ${localTime}, Remote: ${remoteTime}. Prioritizing Remote.`);
                     noteActions.downloadUpdate.push({ filename, fileId: remoteFile.id }); // Prefer remote if timestamps broken
                } else if (localTime > remoteTime + buffer) {
                    noteActions.uploadUpdate.push({ key, noteData: localNote, fileId: remoteFile.id });

                    // Store the fileId in the local note for future reference
                    if (!localNote.fileId) {
                        localNote.fileId = remoteFile.id;
                        await chrome.storage.local.set({ [key]: localNote });
                        console.log(`BG SYNC: Updated local note ${key} with fileId ${remoteFile.id}`);
                    }
                } else if (remoteTime > localTime + buffer) {
                    noteActions.downloadUpdate.push({ filename, fileId: remoteFile.id });
                }
                remoteNoteFilenames.delete(filename); // Mark as processed
            } else { // Exists only locally
                noteActions.uploadNew.push({ key, noteData: localNote });
            }
        }
        // Iterate through remaining remote files (exist only remotely)
        for (const filename of remoteNoteFilenames) {
             const remoteFile = remoteNotesMap[filename];
            noteActions.downloadNew.push({ filename, fileId: remoteFile.id });
        }
        console.log(`BG SYNC: Note Actions Queued:`, noteActions);

        // 5. Execute Actions (Metadata First, then Notes)

        // --- Metadata Actions ---
        if (downloadMetadata) {
            // We already validated remoteNotebooksData during comparison phase
            updateAndNotifyStatus('syncing', `Downloading notebook structure...`);
            try {
                if (remoteNotebooksData) { // Double check it's valid
                    // Preserve locally deleted notebooks
                    if (Array.isArray(localNotebooksData) && Array.isArray(localNotebooksData._deletedIds) && localNotebooksData._deletedIds.length > 0) {
                        console.log("BG: Preserving locally deleted notebook IDs during download");

                        // Filter out any notebooks from remote data that are in the local deleted list
                        if (Array.isArray(remoteNotebooksData)) {
                            const deletedIds = localNotebooksData._deletedIds;
                            remoteNotebooksData = remoteNotebooksData.filter(nb =>
                                nb && typeof nb.id === 'string' && !deletedIds.includes(nb.id)
                            );

                            // Preserve the deleted IDs list in the remote data
                            remoteNotebooksData._deletedIds = deletedIds;
                        }
                    }

                    await chrome.storage.local.set({ [NOTEBOOKS_STORAGE_KEY]: remoteNotebooksData });
                    console.log("BG: Successfully downloaded and saved validated notebook metadata.");
                    localNotebooksData = remoteNotebooksData; // Update local memory immediately
                } else {
                     throw new Error("Cannot download metadata, remote data was invalid.");
                }
            } catch (e) {
                console.error("BG: Error saving downloaded metadata locally:", e);
                updateAndNotifyStatus('error', `Metadata download save failed`);
                metadataSuccess = false; // Mark failure
            }
        } else if (uploadMetadata) {
             updateAndNotifyStatus('syncing', `Uploading notebook structure...`);
            try {
                // Ensure localNotebooksData has a timestamp before upload
                if (Array.isArray(localNotebooksData) && !localNotebooksData._lastUpdated) { // Check if array first
                    localNotebooksData._lastUpdated = Date.now();
                }
                // Only upload if local data is actually an array
                if(Array.isArray(localNotebooksData)) {
                    await uploadMetadataFile(token, localNotebooksData, remoteMetadata);
                } else {
                    console.error("BG: Attempted to upload metadata, but localNotebooksData is not an array. Skipping upload.");
                    metadataSuccess = false; // Mark failure
                }
            } catch (e) {
                console.error("BG: Error during metadata upload:", e);
                updateAndNotifyStatus('error', `Metadata upload failed`);
                metadataSuccess = false; // Mark failure
            }
        }
        // --- End Metadata Actions ---

        // --- Note Actions ---
        const totalNoteActions = noteActions.uploadNew.length + noteActions.uploadUpdate.length +
                                noteActions.downloadNew.length + noteActions.downloadUpdate.length +
                                noteActions.deleteLocal.length;
        let noteErrors = 0;

        if (totalNoteActions > 0) {
            updateAndNotifyStatus('syncing', `Syncing ${totalNoteActions} notes...`);

            // Handle local deletions first
            if (noteActions.deleteLocal.length > 0) {
                console.log(`BG SYNC: Deleting ${noteActions.deleteLocal.length} local notes that were removed remotely`);
                for (const action of noteActions.deleteLocal) {
                    try {
                        await chrome.storage.local.remove(action.key);
                        console.log(`BG SYNC: Successfully deleted local note ${action.key}`);
                    } catch (error) {
                        console.error(`BG SYNC: Error deleting local note ${action.key}:`, error);
                        noteErrors++;
                    }
                }
            }

            const downloadNotePromises = [
                ...noteActions.downloadNew.map(a => downloadAndSaveNote(token, a.fileId, a.filename)),
                ...noteActions.downloadUpdate.map(a => downloadAndSaveNote(token, a.fileId, a.filename)),
            ];
            const downloadResults = await Promise.allSettled(downloadNotePromises);
            downloadResults.forEach(result => {
                if (result.status === 'rejected') {
                    console.error('BG SYNC Download Note Error:', result.reason);
                    noteErrors++;
                }
            });
            console.log("BG: Download Note actions completed.");

            const uploadNotePromises = [
                ...noteActions.uploadNew.map(a => uploadNote(token, a.key, a.noteData, undefined)),
                ...noteActions.uploadUpdate.map(a => uploadNote(token, a.key, a.noteData, a.fileId)),
            ];
            const uploadResults = await Promise.allSettled(uploadNotePromises);
             uploadResults.forEach(result => {
                if (result.status === 'rejected') {
                    console.error('BG SYNC Upload Note Error:', result.reason);
                    noteErrors++;
                }
            });
             console.log("BG: Upload Note actions completed.");

        } else {
            console.log("BG: No note sync actions needed.");
        }

        // 6. Final Status
        if (noteErrors > 0 || !metadataSuccess) {
             updateAndNotifyStatus('error', `Sync finished with ${noteErrors} note errors, ${metadataSuccess ? 'metadata OK' : 'metadata error'}`);
        } else {
            updateAndNotifyStatus('synced', `Sync complete (${new Date().toLocaleTimeString()})`);
        }
        console.log("BG: --- Synchronization Process Finished ---");

    } catch (error) {
        console.error("BG: CRITICAL Error during synchronization process:", error);
        // Handle specific errors with user-friendly messages
        if (error.message.includes("Authentication") || error.message.includes("token invalid") || error.message.includes("No user approved") || error.message.includes("rejected the request")) {
            console.log("BG: Authentication error during sync, disabling sync.");
            await removeCachedAuthToken(currentToken);
            await chrome.storage.local.set({ driveSyncEnabled: false });
            notifyPopups('updateConnectionStatus', { isConnected: false });
            updateAndNotifyStatus('disconnected', 'Google Drive authentication expired. Please reconnect in settings');
        } else if (error.message.includes("offline") || error.message.includes("network")) {
            updateAndNotifyStatus('error', 'No internet connection. Sync will retry when online');
        } else if (error.message.includes("quota") || error.message.includes("storage")) {
            updateAndNotifyStatus('error', 'Google Drive storage quota exceeded. Please free up space');
        } else if (error.message.includes("rate limit") || error.message.includes("429")) {
            updateAndNotifyStatus('error', 'Google Drive rate limit reached. Sync will retry automatically');
        } else if (error.message.includes("permission") || error.message.includes("403")) {
            updateAndNotifyStatus('error', 'Google Drive permission denied. Please check folder access');
        } else {
            // Provide a more user-friendly generic error message
            const friendlyMessage = error.message.length > 100 ?
                'Sync failed due to an unexpected error. Please try again' :
                `Sync failed: ${error.message}`;
            updateAndNotifyStatus('error', friendlyMessage);
        }
    } finally {
        await releaseSyncLock(); // Ensure lock is always released
    }
}


// --- Context Menu Setup ---
function setupContextMenus() {
    chrome.contextMenus.removeAll(() => {
        if (chrome.runtime.lastError) {
            console.warn("BG: Error removing all context menus:", chrome.runtime.lastError.message);
            // Proceed anyway, might work
        }

        // 1. Parent Highlight Menu
        chrome.contextMenus.create({
            id: CONTEXT_MENU_ID_PARENT_HIGHLIGHT,
            title: "Stashy Highlight",
            contexts: ["selection"]
        }, () => { if (chrome.runtime.lastError) console.warn("BG: Parent Highlight menu creation error:", chrome.runtime.lastError.message); });

        // 2. Color Sub-Menus (Remain under Parent)
        for (const [, value] of Object.entries(HIGHLIGHT_COLORS_BG)) {
            chrome.contextMenus.create({
                id: `highlightColor-${value.color}`, // Unique ID like highlightColor-yellow
                parentId: CONTEXT_MENU_ID_PARENT_HIGHLIGHT,
                title: `Highlight as ${value.title}`,
                contexts: ["selection"]
            }, () => { if (chrome.runtime.lastError) console.warn(`BG: Highlight Color (${value.color}) menu creation error:`, chrome.runtime.lastError.message); });
        }

        // 3. Add Separator
        chrome.contextMenus.create({
            id: "highlightSeparator",
            parentId: CONTEXT_MENU_ID_PARENT_HIGHLIGHT,
            type: "separator",
            contexts: ["selection"]
        }, () => { if (chrome.runtime.lastError) console.warn("BG: Separator creation error:", chrome.runtime.lastError.message); });

        // 4. Style Sub-Menus (Under Parent)
        chrome.contextMenus.create({
            id: CONTEXT_MENU_ID_HIGHLIGHT_UNDERLINE,
            parentId: CONTEXT_MENU_ID_PARENT_HIGHLIGHT,
            title: "Highlight as Underline",
            contexts: ["selection"]
        }, () => { if (chrome.runtime.lastError) console.warn("BG: Highlight Underline menu creation error:", chrome.runtime.lastError.message); });

        chrome.contextMenus.create({
            id: CONTEXT_MENU_ID_HIGHLIGHT_WAVY,
            parentId: CONTEXT_MENU_ID_PARENT_HIGHLIGHT,
            title: "Highlight as Wavy Underline",
            contexts: ["selection"]
        }, () => { if (chrome.runtime.lastError) console.warn("BG: Highlight Wavy menu creation error:", chrome.runtime.lastError.message); });

        chrome.contextMenus.create({
            id: CONTEXT_MENU_ID_HIGHLIGHT_BORDER,
            parentId: CONTEXT_MENU_ID_PARENT_HIGHLIGHT,
            title: "Highlight as Thick Border",
            contexts: ["selection"]
        }, () => { if (chrome.runtime.lastError) console.warn("BG: Highlight Border menu creation error:", chrome.runtime.lastError.message); });

        // Add Strikethrough Sub-Menu
        chrome.contextMenus.create({
            id: CONTEXT_MENU_ID_HIGHLIGHT_STRIKETHROUGH,
            parentId: CONTEXT_MENU_ID_PARENT_HIGHLIGHT,
            title: "Highlight as Strikethrough",
            contexts: ["selection"]
        }, () => { if (chrome.runtime.lastError) console.warn("BG: Highlight Strikethrough menu creation error:", chrome.runtime.lastError.message); });




        // 5. Add Highlight with Note option
        chrome.contextMenus.create({
            id: CONTEXT_MENU_ID_HIGHLIGHT_WITH_NOTE,
            parentId: CONTEXT_MENU_ID_PARENT_HIGHLIGHT,
            title: "Highlight with Note",
            contexts: ["selection"]
        }, () => { if (chrome.runtime.lastError) console.warn("BG: Context menu 'Highlight with Note' creation error:", chrome.runtime.lastError.message); });

        // 6. Add Separator for Snippet Highlights
        chrome.contextMenus.create({
            id: "highlightSnippetSeparator",
            parentId: CONTEXT_MENU_ID_PARENT_HIGHLIGHT,
            type: "separator",
            contexts: ["selection"]
        }, () => { if (chrome.runtime.lastError) console.warn("BG: Snippet Separator creation error:", chrome.runtime.lastError.message); });

        // 7. Add Review Pro Snippet option
        chrome.contextMenus.create({
            id: CONTEXT_MENU_ID_HIGHLIGHT_REVIEW_PRO,
            parentId: CONTEXT_MENU_ID_PARENT_HIGHLIGHT,
            title: "Highlight as Review Pro Snippet",
            contexts: ["selection"]
        }, () => { if (chrome.runtime.lastError) console.warn("BG: Context menu 'Highlight as Review Pro Snippet' creation error:", chrome.runtime.lastError.message); });

        // 8. Add Review Con Snippet option
        chrome.contextMenus.create({
            id: CONTEXT_MENU_ID_HIGHLIGHT_REVIEW_CON,
            parentId: CONTEXT_MENU_ID_PARENT_HIGHLIGHT,
            title: "Highlight as Review Con Snippet",
            contexts: ["selection"]
        }, () => { if (chrome.runtime.lastError) console.warn("BG: Context menu 'Highlight as Review Con Snippet' creation error:", chrome.runtime.lastError.message); });

        // 9. Add Spec Snippet option
        chrome.contextMenus.create({
            id: CONTEXT_MENU_ID_HIGHLIGHT_SPEC,
            parentId: CONTEXT_MENU_ID_PARENT_HIGHLIGHT,
            title: "Highlight as Spec Snippet",
            contexts: ["selection"]
        }, () => { if (chrome.runtime.lastError) console.warn("BG: Context menu 'Highlight as Spec Snippet' creation error:", chrome.runtime.lastError.message); });

        // 10. Add Separator for Snippet Highlights with Note
        chrome.contextMenus.create({
            id: "highlightSnippetWithNoteSeparator",
            parentId: CONTEXT_MENU_ID_PARENT_HIGHLIGHT,
            type: "separator",
            contexts: ["selection"]
        }, () => { if (chrome.runtime.lastError) console.warn("BG: Snippet With Note Separator creation error:", chrome.runtime.lastError.message); });

        // 11. Add Review Pro Snippet with Note option
        chrome.contextMenus.create({
            id: CONTEXT_MENU_ID_HIGHLIGHT_REVIEW_PRO_WITH_NOTE,
            parentId: CONTEXT_MENU_ID_PARENT_HIGHLIGHT,
            title: "Highlight as Review Pro Snippet with Note",
            contexts: ["selection"]
        }, () => { if (chrome.runtime.lastError) console.warn("BG: Context menu 'Highlight as Review Pro Snippet with Note' creation error:", chrome.runtime.lastError.message); });

        // 12. Add Review Con Snippet with Note option
        chrome.contextMenus.create({
            id: CONTEXT_MENU_ID_HIGHLIGHT_REVIEW_CON_WITH_NOTE,
            parentId: CONTEXT_MENU_ID_PARENT_HIGHLIGHT,
            title: "Highlight as Review Con Snippet with Note",
            contexts: ["selection"]
        }, () => { if (chrome.runtime.lastError) console.warn("BG: Context menu 'Highlight as Review Con Snippet with Note' creation error:", chrome.runtime.lastError.message); });

        // 13. Add Spec Snippet with Note option
        chrome.contextMenus.create({
            id: CONTEXT_MENU_ID_HIGHLIGHT_SPEC_WITH_NOTE,
            parentId: CONTEXT_MENU_ID_PARENT_HIGHLIGHT,
            title: "Highlight as Spec Snippet with Note",
            contexts: ["selection"]
        }, () => { if (chrome.runtime.lastError) console.warn("BG: Context menu 'Highlight as Spec Snippet with Note' creation error:", chrome.runtime.lastError.message); });

        // 10. Add to Note Menu (Separate Top-Level Menu)
        chrome.contextMenus.create({
            id: CONTEXT_MENU_ID_NOTE_FROM_SELECTION,
            title: "Add Selection to Stashy",
            contexts: ["selection"]
        }, () => { if (chrome.runtime.lastError) console.warn("BG: Context menu 'Add to Note' creation error:", chrome.runtime.lastError.message); });

        // Note: Chrome extensions cannot completely override the default context menu
        // Instead, we'll create a custom context menu that appears on right-click
        // The actual implementation will be handled in the content script
    });
}

// --- Event Listeners ---

// Create/Update menus on install or update, check sync
chrome.runtime.onInstalled.addListener((details) => {
    console.log("BG: onInstalled event triggered, reason:", details.reason);
    setupContextMenus();
    chrome.alarms.clear("periodicSync"); // Clear any old sync alarm

    if (details.reason === "install") {
        // Set initial disconnected status on first install
        chrome.storage.local.set({ lastSyncStatus: 'disconnected', driveSyncEnabled: false });
        console.log("BG: First install, set sync status to disconnected.");
    } else if (details.reason === "update") {
        // On update, check if sync was enabled and restart periodic sync if needed
        chrome.storage.local.get('driveSyncEnabled', result => {
            if (result.driveSyncEnabled) {
                 console.log("BG: Re-enabling periodic sync after update.");
                 synchronizeNotes(); // Run sync check after update
                 chrome.alarms.create("periodicSync", { periodInMinutes: 30 });
             } else {
                  console.log("BG: Sync was disabled, no sync started after update.");
             }
        });
    }
});

// Check sync on browser startup
chrome.runtime.onStartup.addListener(() => {
    console.log("BG: onStartup event triggered.");
    setupContextMenus(); // Setup context menus again
    chrome.alarms.clear("periodicSync"); // Clear old sync alarm if any
    // Check Drive connection on browser startup if enabled
     chrome.storage.local.get('driveSyncEnabled', result => {
         if (result.driveSyncEnabled) {
             console.log("BG: Sync enabled on startup, initiating sync check.");
             synchronizeNotes(); // Run sync on startup
             chrome.alarms.create("periodicSync", { periodInMinutes: 30 });
         } else {
             chrome.storage.local.set({ lastSyncStatus: 'disconnected' }); // Ensure status reflects reality
             console.log("BG: Sync disabled on startup.");
         }
     });
});

// Listener for context menu clicks
chrome.contextMenus.onClicked.addListener((info, tab) => {
    if (!tab?.id) {
        console.error("Stashy BG: Context menu clicked, but no valid tab found.");
        return;
    }

    let messagePayload = null;

    // Check if it's a highlight color menu item
    if (info.menuItemId.startsWith("highlightColor-")) {
        const color = info.menuItemId.split("-")[1] || DEFAULT_HIGHLIGHT_COLOR_BG;
        messagePayload = { action: "highlightSelection", color: color, style: 'color' };
    }
    // Check if it's a highlight style menu item
    else if (info.menuItemId.startsWith("StashyHighlightStyle-")) {
        const style = info.menuItemId.split("-")[1]; // e.g., 'underline', 'wavy', 'border-thick', 'strikethrough'
        // Ensure 'border-thick' is correctly spelled in this array check
        if (['underline', 'wavy', 'border-thick', 'strikethrough'].includes(style)) {
             messagePayload = { action: "highlightSelection", color: null, style: style };
        }
    }
    // Check if it's the highlight with note item
    else if (info.menuItemId === CONTEXT_MENU_ID_HIGHLIGHT_WITH_NOTE) {
        messagePayload = { action: "highlightWithNote", color: DEFAULT_HIGHLIGHT_COLOR_BG, style: 'color' };
    }

    // Check if it's the review pro snippet item
    else if (info.menuItemId === CONTEXT_MENU_ID_HIGHLIGHT_REVIEW_PRO) {
        messagePayload = { action: "highlightReviewSnippet", snippetType: "pro", color: DEFAULT_HIGHLIGHT_COLOR_BG, style: 'color' };
    }
    // Check if it's the review con snippet item
    else if (info.menuItemId === CONTEXT_MENU_ID_HIGHLIGHT_REVIEW_CON) {
        messagePayload = { action: "highlightReviewSnippet", snippetType: "con", color: DEFAULT_HIGHLIGHT_COLOR_BG, style: 'color' };
    }
    // Check if it's the spec snippet item
    else if (info.menuItemId === CONTEXT_MENU_ID_HIGHLIGHT_SPEC) {
        messagePayload = { action: "highlightSpecSnippet", color: DEFAULT_HIGHLIGHT_COLOR_BG, style: 'color' };
    }
    // Check if it's the review pro snippet with note item
    else if (info.menuItemId === CONTEXT_MENU_ID_HIGHLIGHT_REVIEW_PRO_WITH_NOTE) {
        messagePayload = { action: "highlightReviewSnippetWithNote", snippetType: "pro", color: DEFAULT_HIGHLIGHT_COLOR_BG, style: 'color' };
    }
    // Check if it's the review con snippet with note item
    else if (info.menuItemId === CONTEXT_MENU_ID_HIGHLIGHT_REVIEW_CON_WITH_NOTE) {
        messagePayload = { action: "highlightReviewSnippetWithNote", snippetType: "con", color: DEFAULT_HIGHLIGHT_COLOR_BG, style: 'color' };
    }
    // Check if it's the spec snippet with note item
    else if (info.menuItemId === CONTEXT_MENU_ID_HIGHLIGHT_SPEC_WITH_NOTE) {
        messagePayload = { action: "highlightSpecSnippetWithNote", color: DEFAULT_HIGHLIGHT_COLOR_BG, style: 'color' };
    }
    // Check if it's the add to note item
    else if (info.menuItemId === CONTEXT_MENU_ID_NOTE_FROM_SELECTION) {
        messagePayload = { action: "noteFromSelection" };
    }


    // Send the message if a payload was constructed
    if (messagePayload) {
        // Re-confirming this log location:
        console.log(`BG: Sending message to tab ${tab.id}:`, messagePayload);
        chrome.tabs.sendMessage(tab.id, messagePayload)
            .catch(err => console.log(`BG: Failed to send message (${messagePayload.action}) to tab ${tab.id}:`, err?.message || err));
    } else {
        // If the border-thick click ends up here, the ID or includes() check is wrong
        console.warn(`BG: Unhandled context menu item ID: ${info.menuItemId}`);
    }
});


// Listener for Alarms (Sync, Reminders, and Cleanup)
chrome.alarms.onAlarm.addListener((alarm) => {
    if (alarm.name === "periodicSync") {
        console.log("BG: Periodic sync alarm triggered.");
        synchronizeNotes(); // Run the sync process
        return; // Don't process as reminder
    }

    if (alarm.name === "cleanupExpiredReminders") {
        console.log("BG: Cleanup alarm triggered.");
        cleanupExpiredReminders(); // Run the cleanup process
        return; // Don't process as reminder
    }

    // Handle Reminder Alarms
    const parts = alarm.name.split('::');
    if (parts[0] === 'reminder' && parts.length === 3) {
        const url = parts[1]; const noteIndexStr = parts[2];
        const noteIndex = parseInt(noteIndexStr);
        if (!isNaN(noteIndex) && url) {
            if (typeof STORAGE_KEY_PREFIX === 'undefined') {
                console.error("BG: STORAGE_KEY_PREFIX is not defined in onAlarm handler");
                return;
            }

            const noteKey = `${STORAGE_KEY_PREFIX}${url}_note${noteIndex}`;
            console.log(`BG: Processing alarm for note key: ${noteKey}`);
            chrome.storage.local.get([noteKey], (result) => {
                 if (chrome.runtime.lastError) { console.error("BG: Error retrieving note locally for alarm:", chrome.runtime.lastError); return; }
                const note = result[noteKey];
                if (note && note.reminder && note.reminder <= Date.now() + 60000) { // Allow 1 min past due
                    const plainText = stripHtml(note.text || '').substring(0, 100);
                    // Show Browser Notification
                    chrome.notifications.create(alarm.name, {
                        type: 'basic',
                        iconUrl: 'icon128.png',
                        title: `Stashy Reminder (Note ${noteIndex})`,
                        message: plainText || 'Check your Stashy!',
                        priority: 2,
                        contextMessage: url.length > 50 ? url.substring(0, 47) + '...' : url
                    }, (id) => {
                         if (chrome.runtime.lastError) console.error("BG: Error creating notification:", chrome.runtime.lastError);
                         else console.log("BG: Reminder notification shown:", id);
                          // Clear the reminder field from the note data *after* showing notification
                         if (note && note.reminder) {
                            // If there's a calendar event associated with this reminder, remove it
                            if (note.calendarEventId) {
                                removeCalendarEvent(note.calendarEventId)
                                    .then(success => {
                                        if (success) {
                                            console.log(`BG: Removed calendar event ${note.calendarEventId}`);
                                        } else {
                                            console.warn(`BG: Failed to remove calendar event ${note.calendarEventId}`);
                                            // We'll still clear the calendarEventId below
                                        }
                                    })
                                    .catch(error => {
                                        console.error("BG: Error removing calendar event:", error);
                                        // We'll still clear the calendarEventId below
                                    })
                                    .finally(() => {
                                        // Always clear the calendarEventId, even if there was an error
                                        // This prevents repeated failed attempts
                                        note.calendarEventId = null;
                                    });
                            }

                            note.reminder = null; // Clear reminder time
                            note.calendarEventId = null; // Clear calendar event ID
                             chrome.storage.local.set({ [noteKey]: note }, () => {
                                 if (chrome.runtime.lastError) console.warn("BG: Error clearing reminder time from note:", chrome.runtime.lastError.message);
                                 else console.log("BG: Cleared reminder time from note:", noteKey);
                             });
                          }
                          chrome.alarms.clear(alarm.name); // Clear the alarm itself
                    });
                 } else {
                      console.log(`BG: Reminder ${alarm.name} no longer valid or note/reminder deleted. Clearing alarm.`);

                      // Check if there's a calendar event to remove even though the reminder is gone
                      if (note && note.calendarEventId) {
                          console.log(`BG: Found calendar event ${note.calendarEventId} for deleted reminder. Removing...`);

                          removeCalendarEvent(note.calendarEventId)
                              .then(success => {
                                  if (success) {
                                      console.log(`BG: Removed calendar event ${note.calendarEventId} for deleted reminder`);

                                      // Update the note to remove the calendar event ID
                                      note.calendarEventId = null;
                                      chrome.storage.local.set({ [noteKey]: note }, () => {
                                          if (chrome.runtime.lastError) {
                                              console.warn("BG: Error updating note after removing calendar event:", chrome.runtime.lastError.message);
                                          } else {
                                              console.log(`BG: Updated note ${noteKey} to remove calendar event ID`);
                                          }
                                      });
                                  } else {
                                      console.warn(`BG: Failed to remove calendar event ${note.calendarEventId} for deleted reminder`);
                                  }
                              })
                              .catch(error => {
                                  console.error("BG: Error removing calendar event for deleted reminder:", error);
                              });
                      }

                      chrome.alarms.clear(alarm.name);
                 }
            });
        } else {
             console.warn(`BG: Invalid reminder alarm name format: ${alarm.name}. Clearing.`);
             chrome.alarms.clear(alarm.name);
        }
    }
});

// --- Periodic Cleanup for Expired Reminders and Screenshots ---
/**
 * Checks for expired reminders and removes their calendar events
 * Also checks for screenshots scheduled for deletion
 */
async function cleanupExpiredReminders() {
    console.log("BG: Running cleanup for expired reminders and screenshots");

    try {
        // Check if service worker is available
        if (typeof navigator.serviceWorker === 'undefined') {
            console.warn("BG: Error cleaning up expired reminders: No SW");
            return; // Exit early if service worker is not available
        }

        // Get all notes from storage
        const allItems = await chrome.storage.local.get(null);
        let cleanupCount = 0;

        for (const [key, value] of Object.entries(allItems)) {
            // Check if it's a note with a calendar event
            if (key.startsWith(STORAGE_KEY_PREFIX) &&
                typeof value === 'object' &&
                value &&
                value.calendarEventId) {

                // Case 1: Reminder has expired
                if (value.reminder && value.reminder < Date.now()) {
                    console.log(`BG: Found expired reminder in note ${key} with calendar event ${value.calendarEventId}`);

                    try {
                        // Remove the calendar event
                        const success = await removeCalendarEvent(value.calendarEventId);

                        if (success) {
                            console.log(`BG: Removed calendar event ${value.calendarEventId} for expired reminder`);

                            // Update the note to remove the calendar event ID
                            value.calendarEventId = null;
                            await chrome.storage.local.set({ [key]: value });
                            console.log(`BG: Updated note ${key} to remove calendar event ID`);
                            cleanupCount++;
                        } else {
                            console.warn(`BG: Failed to remove calendar event ${value.calendarEventId} for expired reminder`);

                            // Even if the API call failed, we should still remove the calendar event ID
                            // to prevent repeated failed attempts
                            value.calendarEventId = null;
                            await chrome.storage.local.set({ [key]: value });
                            console.log(`BG: Updated note ${key} to remove calendar event ID despite API failure`);
                        }
                    } catch (error) {
                        console.error(`BG: Error removing calendar event ${value.calendarEventId}:`, error);

                        // Even if an error occurred, we should still remove the calendar event ID
                        // to prevent repeated failed attempts
                        value.calendarEventId = null;
                        await chrome.storage.local.set({ [key]: value });
                        console.log(`BG: Updated note ${key} to remove calendar event ID despite error`);
                    }
                }
                // Case 2: Reminder has been removed but calendar event still exists
                else if (!value.reminder) {
                    console.log(`BG: Found note ${key} with calendar event ${value.calendarEventId} but no reminder`);

                    try {
                        // Remove the calendar event
                        const success = await removeCalendarEvent(value.calendarEventId);

                        if (success) {
                            console.log(`BG: Removed orphaned calendar event ${value.calendarEventId}`);

                            // Update the note to remove the calendar event ID
                            value.calendarEventId = null;
                            await chrome.storage.local.set({ [key]: value });
                            console.log(`BG: Updated note ${key} to remove calendar event ID`);
                            cleanupCount++;
                        } else {
                            console.warn(`BG: Failed to remove orphaned calendar event ${value.calendarEventId}`);

                            // Even if the API call failed, we should still remove the calendar event ID
                            // to prevent repeated failed attempts
                            value.calendarEventId = null;
                            await chrome.storage.local.set({ [key]: value });
                            console.log(`BG: Updated note ${key} to remove calendar event ID despite API failure`);
                        }
                    } catch (error) {
                        console.error(`BG: Error removing orphaned calendar event ${value.calendarEventId}:`, error);

                        // Even if an error occurred, we should still remove the calendar event ID
                        // to prevent repeated failed attempts
                        value.calendarEventId = null;
                        await chrome.storage.local.set({ [key]: value });
                        console.log(`BG: Updated note ${key} to remove calendar event ID despite error`);
                    }
                }
            }
        }

        if (cleanupCount > 0) {
            console.log(`BG: Cleaned up ${cleanupCount} calendar events`);
        } else {
            console.log("BG: No calendar events needed cleanup");
        }

        // Now check for screenshots scheduled for deletion
        try {
            // Get scheduled screenshot deletions from storage
            const scheduledDeletions = await chrome.storage.local.get(['scheduledScreenshotDeletions']);
            const deletions = scheduledDeletions.scheduledScreenshotDeletions || [];

            if (deletions.length === 0) {
                console.log("BG: No screenshots scheduled for deletion");
                return;
            }

            console.log(`BG: Found ${deletions.length} screenshots scheduled for deletion`);

            // Filter out screenshots that are due for deletion
            const now = Date.now();
            const dueDeletions = deletions.filter(item => item.deletionTime <= now);
            const remainingDeletions = deletions.filter(item => item.deletionTime > now);

            if (dueDeletions.length === 0) {
                console.log("BG: No screenshots due for deletion yet");
                return;
            }

            console.log(`BG: ${dueDeletions.length} screenshots are due for deletion`);

            // Get auth token for Drive API
            const token = await getAuthToken(false);
            let deletedCount = 0;

            // Delete each screenshot
            for (const deletion of dueDeletions) {
                try {
                    console.log(`BG: Deleting screenshot with ID ${deletion.fileId}`);

                    // Delete the file from Drive
                    const deleteUrl = `${DRIVE_API_BASE_URL}/files/${deletion.fileId}`;
                    const deleteResponse = await fetchWithRetry(deleteUrl, {
                        method: 'DELETE',
                        headers: { 'Authorization': `Bearer ${token}` }
                    });

                    if (deleteResponse.status === 404) {
                        console.log(`BG: Screenshot ${deletion.fileId} already deleted`);
                        deletedCount++;
                    } else if (deleteResponse.ok) {
                        console.log(`BG: Successfully deleted screenshot ${deletion.fileId}`);
                        deletedCount++;
                    } else {
                        console.error(`BG: Failed to delete screenshot ${deletion.fileId}: ${deleteResponse.status}`);
                    }
                } catch (deleteError) {
                    console.error(`BG: Error deleting screenshot ${deletion.fileId}:`, deleteError);
                }
            }

            // Update the scheduled deletions in storage
            await chrome.storage.local.set({ scheduledScreenshotDeletions: remainingDeletions });
            console.log(`BG: Deleted ${deletedCount} screenshots, ${remainingDeletions.length} remaining scheduled`);

        } catch (screenshotError) {
            console.error("BG: Error cleaning up screenshots:", screenshotError);
        }
    } catch (error) {
        console.error("BG: Error cleaning up expired reminders:", error);
    }
}

// Set up an alarm to run the cleanup function periodically
chrome.alarms.create("cleanupExpiredReminders", {
    periodInMinutes: 15 // Run every 15 minutes
});

// The cleanup alarm is handled in the main onAlarm listener

// Run the cleanup function when the extension starts
cleanupExpiredReminders();

// --- Google Calendar Integration ---
/**
 * Strips HTML tags from a string
 * @param {string} html - The HTML string to strip
 * @returns {string} - The plain text without HTML tags
 */
function stripHtml(html) {
    if (!html) return '';

    // Simple regex-based HTML tag removal for background script (no DOM access)
    return html
        .replace(/<br\s*\/?>/gi, ' ')  // Replace <br> with space
        .replace(/<[^>]*>/g, '')       // Remove all HTML tags
        .replace(/&nbsp;/g, ' ')        // Replace &nbsp; with space
        .replace(/\s+/g, ' ')           // Replace multiple spaces with single space
        .trim();                        // Trim whitespace
}

/**
 * Adds a reminder to Google Calendar
 * @param {string} url - The URL associated with the note
 * @param {number} reminderTime - The timestamp for the reminder
 * @param {string} noteTitle - The title of the note (optional)
 * @param {string} noteText - The text content of the note (optional)
 * @returns {Promise<string|null>} - Promise resolving to the calendar event ID or null if failed
 */
async function addReminderToCalendar(url, reminderTime, noteTitle, noteText) {
    try {
        console.log("BG: Adding reminder to Google Calendar...");

        // Validate inputs
        if (!url) {
            console.warn("BG: Missing URL for calendar event");
            url = "Unknown URL";
        }

        if (!reminderTime || isNaN(new Date(reminderTime).getTime())) {
            throw new Error("Invalid reminder time");
        }

        // Get auth token with Calendar scope
        console.log("BG: Getting auth token for calendar...");
        const token = await getAuthToken(false);
        if (!token) {
            throw new Error("Failed to get authentication token");
        }

        console.log("BG: Successfully obtained auth token for calendar");

        // Format the reminder time for Google Calendar
        const reminderDate = new Date(reminderTime);

        // Create a summary/title for the event
        const eventTitle = noteTitle ?
            `Stashy Reminder: ${noteTitle}` :
            "Stashy Reminder";

        // Create a description for the event
        let eventDescription = "Reminder from Stashy Chrome Extension\n\n";
        if (noteText) {
            // Strip HTML from note text for the description
            eventDescription += stripHtml(noteText) + "\n\n";
        }
        eventDescription += `Original URL: ${url}`;

        // Create the event object
        const event = {
            summary: eventTitle,
            description: eventDescription,
            start: {
                dateTime: reminderDate.toISOString(),
                timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone
            },
            end: {
                dateTime: new Date(reminderDate.getTime() + 30 * 60000).toISOString(), // 30 minutes duration
                timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone
            },
            reminders: {
                useDefault: true
            }
        };

        // Send the request to Google Calendar API
        console.log("BG: Sending request to Google Calendar API...");
        try {
            const response = await fetch('https://www.googleapis.com/calendar/v3/calendars/primary/events', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(event)
            });

            console.log("BG: Calendar API response status:", response.status);

            if (!response.ok) {
                const errorText = await response.text();
                let errorMessage = response.statusText;

                try {
                    const errorData = JSON.parse(errorText);
                    errorMessage = errorData.error?.message || errorMessage;
                } catch (e) {
                    // If JSON parsing fails, use the raw text
                    errorMessage = errorText || errorMessage;
                }

                throw new Error(`Google Calendar API error (${response.status}): ${errorMessage}`);
            }

            const eventData = await response.json();
            console.log("BG: Successfully created calendar event");
            return eventData.id; // Return the event ID
        } catch (fetchError) {
            console.error("BG: Fetch error when calling Calendar API:", fetchError);
            throw fetchError;
        }
    } catch (error) {
        console.error("BG: Error adding reminder to Google Calendar:", error);
        return null;
    }
}

/**
 * Removes a calendar event
 * @param {string} eventId - The Google Calendar event ID
 * @returns {Promise<boolean>} - Promise resolving to true if successful, false otherwise
 */
async function removeCalendarEvent(eventId) {
    if (!eventId) {
        console.warn("BG: No event ID provided for calendar event removal");
        return false;
    }

    try {
        console.log(`BG: Removing calendar event with ID: ${eventId}`);

        // Check if service worker is available
        if (typeof navigator.serviceWorker === 'undefined') {
            console.warn("BG: Service Worker API not available");
            return false; // Return false instead of throwing an error
        }

        // Get auth token
        console.log("BG: Getting auth token for calendar event removal...");
        const token = await getAuthToken(false);
        if (!token) {
            throw new Error("Failed to get authentication token");
        }

        console.log("BG: Successfully obtained auth token for calendar event removal");

        // Send the request to Google Calendar API
        console.log("BG: Sending DELETE request to Google Calendar API...");
        const response = await fetch(`https://www.googleapis.com/calendar/v3/calendars/primary/events/${eventId}`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });

        console.log("BG: Calendar event removal response status:", response.status);

        // If the response is 410 Gone, the resource has already been deleted
        // This is actually a success case for our purposes
        if (response.status === 410) {
            console.log("BG: Calendar event was already deleted (410 Gone)");
            return true;
        }

        if (!response.ok) {
            // For DELETE requests, we might not get a response body
            let errorMessage = response.statusText;

            try {
                const errorText = await response.text();
                if (errorText) {
                    const errorData = JSON.parse(errorText);
                    errorMessage = errorData.error?.message || errorMessage;
                }
            } catch (e) {
                // If reading the response or JSON parsing fails, use the status text
            }

            throw new Error(`Google Calendar API error (${response.status}): ${errorMessage}`);
        }

        console.log("BG: Successfully removed calendar event");
        return true;
    } catch (error) {
        console.error("BG: Error removing calendar event:", error);

        // Check if the error message indicates the resource was already deleted
        if (error.message && (
            error.message.includes("410") ||
            error.message.toLowerCase().includes("resource has been deleted") ||
            error.message.toLowerCase().includes("not found")
        )) {
            console.log("BG: Calendar event was already deleted based on error message");
            return true; // Consider this a success
        }

        return false;
    }
}

// --- Notification Click Handler ---
chrome.notifications.onClicked.addListener((notificationId) => {
    const parts = notificationId.split('::');
    if (parts[0] === 'reminder' && parts.length === 3) {
        const url = parts[1];
        const noteIndex = parseInt(parts[2]);
        if (!isNaN(noteIndex) && url) {
            // Reuse the logic from popup/dashboard to open/focus the tab
            let targetUrl;
            try { targetUrl = new URL(url).href; } catch (err) { console.error("Error creating URL for notification click:", err); return; }
             if (!targetUrl.startsWith('http:') && !targetUrl.startsWith('https:') && !targetUrl.startsWith('file:')) return; // Security/practicality

            chrome.tabs.query({ url: targetUrl }, (existingTabs) => {
                 if (chrome.runtime.lastError) return;
                 if (existingTabs && existingTabs.length > 0) {
                     const targetTab = existingTabs[0];
                     chrome.tabs.update(targetTab.id, { active: true }, (tab) => {
                          if(tab) chrome.windows.update(tab.windowId, { focused: true });
                     });
                 } else {
                     chrome.tabs.create({ url: targetUrl, active: true });
                 }
                 // Also send message to content script to maybe flash the note if already open?
                 // Find the right tab first though.
                 chrome.tabs.query({ url: targetUrl }, (tabsToMessage) => {
                      if (tabsToMessage && tabsToMessage.length > 0) {
                         chrome.tabs.sendMessage(tabsToMessage[0].id, { action: 'showReminder', url: url, noteIndex: noteIndex })
                              .catch(e => console.log("Couldn't message content script after notification click:", e?.message));
                      }
                 });
            });
        }
        chrome.notifications.clear(notificationId); // Clear notification after click
    }
});


// --- Message Handling ---
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
    let isResponseAsync = false;

    switch (message.action) {
        case 'injectContentScripts':
            // Handle content script injection requests from popup
            if (!validateMessageSender(sender, false)) { // Don't require content script for popup messages
                console.warn(`BG: Ignoring 'injectContentScripts' from unexpected sender:`, sender);
                sendResponse({ success: false, error: 'Unauthorized sender' });
                return false;
            }

            isResponseAsync = true;
            const targetTabId = message.tabId || (sender.tab ? sender.tab.id : null);

            if (!targetTabId) {
                sendResponse({ success: false, error: 'No tab ID provided' });
                break;
            }

            console.log(`BG: Injecting content scripts into tab ${targetTabId} (requested by popup)`);
            injectContentScripts(targetTabId)
                .then(success => {
                    console.log(`BG: Content script injection ${success ? 'successful' : 'failed'} for tab ${targetTabId}`);
                    sendResponse({ success: success });
                })
                .catch(error => {
                    console.error('BG: Error injecting content scripts:', error);
                    sendResponse({ success: false, error: error.message });
                });
            break;

        case 'voiceSettingsUICreated':
            // Validate sender - this should come from a content script
            if (!validateMessageSender(sender, true)) {
                console.warn(`BG: Ignoring 'voiceSettingsUICreated' from unexpected sender:`, sender);
                return false; // Reject message
            }

            // Forward the voice settings UI HTML to the popup
            chrome.runtime.sendMessage({
                action: 'updateVoiceSettingsUI',
                html: message.html
            });
            break;

        case 'openDashboard':
            // Handle request to open dashboard from content script
            console.log('BG: Received request to open dashboard');
            try {
                chrome.tabs.create({
                    url: chrome.runtime.getURL('dashboard.html'),
                    active: true
                });
                sendResponse({ success: true });
            } catch (error) {
                console.error('BG: Error opening dashboard:', error);
                sendResponse({ success: false, error: error.message });
            }
            break;

        case 'connectGoogleDrive':
            // Validate sender - this should only come from our extension UI
            if (!validateMessageSender(sender)) {
                console.warn(`BG: Ignoring 'connectGoogleDrive' from unexpected sender:`, sender);
                return false; // Reject message
            }

            // --- Premium Check for Google Drive Connection ---
            try {
                const premiumStatus = await chrome.runtime.sendMessage({
                    action: 'getPremiumStatus'
                });

                if (premiumStatus && !premiumStatus.isFeatureAvailable?.('drive_sync')) {
                    console.log('BG: Google Drive connection blocked - premium feature required');
                    updateAndNotifyStatus('disconnected', 'Google Drive sync requires Stashy Pro');
                    sendResponse({
                        status: 'error',
                        message: 'Google Drive sync requires Stashy Pro. Upgrade to unlock cloud synchronization!'
                    });
                    return;
                }
            } catch (premiumError) {
                console.log('BG: Could not check premium status for Drive connection:', premiumError.message);
                // Continue with connection if premium check fails (fallback behavior)
            }

            isResponseAsync = true;
            updateAndNotifyStatus('syncing', 'Authenticating...');
            getAuthToken(true)
                .then(async token => {
                    try {
                        // Get initial startPageToken for Drive changes API
                        await getStartPageToken(token);
                        console.log("BG: Got initial startPageToken for Drive changes API");
                    } catch (error) {
                        console.warn("BG: Failed to get initial startPageToken:", error);
                        // Continue anyway, we'll fall back to full listing
                    }

                    // Use token in this scope

                    chrome.storage.local.set({ driveSyncEnabled: true }, () => {
                        console.log("BG: Drive Sync Enabled flag set.");
                        notifyPopups('updateConnectionStatus', { isConnected: true });
                        // Trigger sync immediately after connection
                        synchronizeNotes(); // Call the main sync function
                        // Set up periodic sync
                        chrome.alarms.create("periodicSync", { periodInMinutes: 30 });
                        console.log("BG: Set periodic sync alarm.");
                    });
                    sendResponse({ status: 'success' });
                })
                .catch(error => {
                    chrome.storage.local.set({ driveSyncEnabled: false }, () => {
                        notifyPopups('updateConnectionStatus', { isConnected: false });
                        updateAndNotifyStatus('disconnected', `Auth failed: ${error.message}`);
                        chrome.alarms.clear("periodicSync"); // Clear periodic sync if auth failed
                    });
                    sendResponse({ status: 'error', message: error.message || 'Failed to get token.' });
                });
            break;

        case 'disconnectGoogleDrive':
             // Validate sender - this should only come from our extension UI
             if (!validateMessageSender(sender)) {
                 console.warn(`BG: Ignoring 'disconnectGoogleDrive' from unexpected sender:`, sender);
                 return false; // Reject message
             }

             isResponseAsync = true;
             updateAndNotifyStatus('syncing', 'Disconnecting...');
             const tokenToRevoke = currentToken; // Capture current token before clearing
             removeCachedAuthToken(tokenToRevoke)
                 .finally(() => {
                      // Clear startPageToken from storage and memory
                      currentStartPageToken = null;
                      chrome.storage.local.remove(DRIVE_START_PAGE_TOKEN_KEY);

                      chrome.storage.local.set({ driveSyncEnabled: false, lastSyncStatus: 'disconnected' }, () => {
                           console.log("BG: Drive Sync Disabled. Sync status set to disconnected.");
                            notifyPopups('updateConnectionStatus', { isConnected: false });
                            updateAndNotifyStatus('disconnected'); // Notify final state
                            chrome.alarms.clear("periodicSync"); // Stop periodic sync
                            console.log("BG: Cleared periodic sync alarm.");
                            screenshotsFolderId = null; // Clear cached folder ID on disconnect
                      });
                      sendResponse({ status: 'success' });
                 });
            break;

        case 'uploadNoteToDrive': // Received from content script on save
            // Validate sender - this should come from a content script
            if (!validateMessageSender(sender, true)) {
                console.warn(`BG: Ignoring 'uploadNoteToDrive' from unexpected sender:`, sender);
                return false; // Reject message
            }

            chrome.storage.local.get('driveSyncEnabled', (result) => {
                 if (result.driveSyncEnabled === true) {
                     console.log(`BG: Received immediate upload trigger for ${message.noteKey}`);
                     getAuthToken(false)
                         .then(token => {
                              return uploadNote(token, message.noteKey, message.noteData, undefined); // Let it find/create ID
                         })
                         .then(() => console.log(`BG: Immediate upload attempt finished for ${message.noteKey}`)) // Simple success log
                         .catch(err => {
                              console.error(`BG: Failed immediate upload trigger for ${message.noteKey}`, err);
                               if (err.message.includes("Auth")) updateAndNotifyStatus('error', `Upload failed: Auth`);
                               else updateAndNotifyStatus('error', `Upload failed`);
                          });
                 }
            });
            break;

         // --- Trigger for Metadata Upload (Optional) ---
         // case 'uploadMetadataFile':
         //     chrome.storage.local.get('driveSyncEnabled', (result) => {
         //         if (result.driveSyncEnabled === true && message.key === NOTEBOOKS_STORAGE_KEY) {
         //             console.log(`BG: Received metadata upload trigger.`);
         //             getAuthToken(false)
         //                 .then(token => listRemoteFiles(token).then(({metadata}) => uploadMetadataFile(token, message.data, metadata)))
         //                 .catch(err => console.error(`BG: Failed metadata upload trigger`, err));
         //         }
         //     });
         //     break;

         case 'checkDriveSync': // Manually trigger a sync
             // Validate sender - this should only come from our extension UI
             if (!validateMessageSender(sender)) {
                 console.warn(`BG: Ignoring 'checkDriveSync' from unexpected sender:`, sender);
                 sendResponse({ status: "error", message: "Unauthorized sender for sync request." });
                 break;
             }

             console.log("BG: Manual sync requested.");
             synchronizeNotes();
             sendResponse({ status: "sync_triggered" });
             break;

        // --- Reminder Handling ---
        case 'setReminder':
             // Validate sender - this should come from our extension
             if (!validateMessageSender(sender)) {
                 console.warn(`BG: Ignoring 'setReminder' from unexpected sender:`, sender);
                 sendResponse({ success: false, error: "Unauthorized sender for setting reminder." });
                 return true; // Keep async flag true
             }

             isResponseAsync = true;
            const { url: remindUrl, noteIndex: remindNoteIndex, reminderTime, addToCalendar, noteTitle, noteText } = message;
            console.log("BG: Received setReminder message with addToCalendar:", addToCalendar);
            const alarmName = `reminder::${remindUrl}::${remindNoteIndex}`;
             chrome.alarms.clear(alarmName, () => {
                 if (reminderTime && !isNaN(reminderTime) && reminderTime > Date.now()) {
                    chrome.alarms.create(alarmName, { when: reminderTime });
                     console.log(`BG: Set reminder alarm ${alarmName} for ${new Date(reminderTime).toLocaleString()}`);

                    // If addToCalendar is true, add the reminder to Google Calendar
                    console.log("BG: Checking addToCalendar flag:", addToCalendar, typeof addToCalendar);
                    if (addToCalendar === true) {
                        console.log("BG: Adding reminder to Google Calendar");
                        addReminderToCalendar(remindUrl, reminderTime, noteTitle, noteText)
                            .then(calendarEventId => {
                                if (calendarEventId) {
                                    console.log(`BG: Added reminder to Google Calendar with event ID: ${calendarEventId}`);
                                    // Store the calendar event ID with the note for future reference
                                    if (typeof STORAGE_KEY_PREFIX === 'undefined') {
                                        console.error("BG: STORAGE_KEY_PREFIX is not defined");
                                        return;
                                    }
                                    const noteKey = `${STORAGE_KEY_PREFIX}${remindUrl}_note${remindNoteIndex}`;
                                    console.log(`BG: Updating note with calendar event ID, key: ${noteKey}`);
                                    chrome.storage.local.get([noteKey], (result) => {
                                        if (chrome.runtime.lastError) {
                                            console.error("BG: Error retrieving note for calendar ID update:", chrome.runtime.lastError);
                                            return;
                                        }

                                        const note = result[noteKey];
                                        if (note) {
                                            note.calendarEventId = calendarEventId;
                                            chrome.storage.local.set({ [noteKey]: note }, () => {
                                                if (chrome.runtime.lastError) {
                                                    console.error("BG: Error updating note with calendar event ID:", chrome.runtime.lastError);
                                                }
                                            });
                                        }
                                    });
                                }
                            })
                            .catch(error => {
                                console.error("BG: Error adding reminder to Google Calendar:", error);
                            });
                    }
                 } else {
                      console.log(`BG: Cleared reminder alarm ${alarmName} (time invalid, past, or null)`);
                 }
                  sendResponse({ success: true }); // Send response after clear/set attempt
             });
            break;

        case 'clearReminder': // Explicit clear request from content script
             // Validate sender - this should come from our extension
             if (!validateMessageSender(sender)) {
                 console.warn(`BG: Ignoring 'clearReminder' from unexpected sender:`, sender);
                 sendResponse({ success: false, error: "Unauthorized sender for clearing reminder." });
                 return true; // Keep async flag true
             }

             isResponseAsync = true;
             const alarmNameToClear = message.alarmName;
            if (alarmNameToClear && alarmNameToClear.startsWith('reminder::')){
                // Extract URL and note index from alarm name
                const parts = alarmNameToClear.split('::');
                if (parts.length === 3) {
                    const url = parts[1];
                    const noteIndex = parts[2];

                    if (typeof STORAGE_KEY_PREFIX === 'undefined') {
                        console.error("BG: STORAGE_KEY_PREFIX is not defined in clearReminder");
                        return;
                    }

                    const noteKey = `${STORAGE_KEY_PREFIX}${url}_note${noteIndex}`;
                    console.log(`BG: Checking for calendar event to remove, key: ${noteKey}`);

                    // Check if there's a calendar event to remove
                    chrome.storage.local.get([noteKey], (result) => {
                        const note = result[noteKey];
                        if (note && note.calendarEventId) {
                            removeCalendarEvent(note.calendarEventId)
                                .then(success => {
                                    if (success) {
                                        console.log(`BG: Removed calendar event ${note.calendarEventId}`);
                                        // Update the note to remove the calendar event ID
                                        note.calendarEventId = null;
                                        chrome.storage.local.set({ [noteKey]: note });
                                    } else {
                                        console.warn(`BG: Failed to remove calendar event ${note.calendarEventId}`);
                                        // Even if the API call failed, we should still remove the calendar event ID
                                        // to prevent repeated failed attempts
                                        note.calendarEventId = null;
                                        chrome.storage.local.set({ [noteKey]: note });
                                        console.log(`BG: Updated note to remove calendar event ID despite API failure`);
                                    }
                                })
                                .catch(error => {
                                    console.error("BG: Error removing calendar event:", error);
                                    // Even if an error occurred, we should still remove the calendar event ID
                                    // to prevent repeated failed attempts
                                    note.calendarEventId = null;
                                    chrome.storage.local.set({ [noteKey]: note });
                                    console.log(`BG: Updated note to remove calendar event ID despite error`);
                                });
                        }
                    });
                }

                // Clear the alarm
                chrome.alarms.clear(alarmNameToClear, (wasCleared) => {
                    if (chrome.runtime.lastError) {
                        console.error(`BG: Error clearing alarm ${alarmNameToClear}:`, chrome.runtime.lastError.message);
                        sendResponse({ success: false, error: chrome.runtime.lastError.message });
                    } else {
                        console.log(`BG: Cleared reminder alarm ${alarmNameToClear} by request.`);
                        sendResponse({ success: wasCleared });
                    }
                });
            } else {
                  sendResponse({ success: false, error: "Invalid alarm name format for clear" });
             }
            break;



        // --- Screenshot Handlers ---
        case 'captureScreenshotWithOptions':
            // Validate sender - this should come from a content script
            if (!validateMessageSender(sender, true)) {
                console.warn(`BG: Ignoring 'captureScreenshotWithOptions' from unexpected sender:`, sender);
                sendResponse({ success: false, error: "Invalid sender for screenshot capture." });
                return true; // Keep async flag true
            }

            isResponseAsync = true;
            const captureOptions = message.options || {};
            const hideStashy = captureOptions.hideStashy || false;
            const captureMode = captureOptions.captureMode || 'visible'; // Get captureMode
            const quality = captureOptions.quality || 90; // Get quality, default 90
            console.log("BG: Received captureScreenshotWithOptions", captureOptions);

            if (!sender.tab?.id || !sender.tab?.windowId) {
                console.error("BG: Capture request missing sender tab ID or window ID.");
                sendResponse({ success: false, error: "Missing sender tab information." });
                return true;
            }

            const tabId = sender.tab.id;
            const windowId = sender.tab.windowId;
            const target = { tabId: tabId }; // Target for debugger commands

            // --- Async function to handle potential UI hiding and capture ---
            const performCapture = async () => {
                let imageDataUrl = null; // Define imageDataUrl outside try block
                try {
                    // **Step 1: Hide UI if requested**
                    if (hideStashy) {
                         console.log(`BG: Requesting content script in tab ${tabId} to hide UI...`);
                         try {
                             // Send message with our extension ID to ensure it's recognized as internal
                             await chrome.tabs.sendMessage(tabId, {
                                 action: 'hideUIForCapture',
                                 _sourceExtensionId: chrome.runtime.id // Add extension ID for validation
                             });
                             await new Promise(resolve => setTimeout(resolve, 150));
                         } catch (e) {
                             console.warn(`BG: Error messaging content script to hide UI (tab ${tabId}): ${e?.message || e}. Proceeding with capture anyway.`);
                         }
                    }

                    // **Step 2: Perform Capture based on mode**
                    if (captureMode === 'full') {
                         console.log("BG: Initiating full page capture...");
                         try {
                             imageDataUrl = await captureFullPage(target, windowId, quality);
                             console.log("BG: Full page capture successful.");
                         } catch (fullPageError) {
                             console.error("BG: Full page capture failed:", fullPageError);
                             console.warn("BG: Falling back to capturing visible area.");
                             try {
                                 imageDataUrl = await chrome.tabs.captureVisibleTab(windowId, { format: 'png', quality: quality });
                             } catch (visibleFallbackError) {
                                 console.error("BG: Fallback captureVisibleTab also failed:", visibleFallbackError);
                                 const combinedError = `Full page capture failed (${fullPageError.message || 'Unknown debugger error'}) and fallback also failed (${visibleFallbackError.message})`;
                                 throw new Error(combinedError);
                             }
                         }
                    } else if (captureMode === 'selectedArea') {
                         console.log("BG: Initiating selected area capture...");
                         const selectionData = captureOptions.selectionData;
                         if (!selectionData) {
                             throw new Error("Selected area capture requested but no selection data provided");
                         }

                         // Add small delay to ensure overlay is completely hidden and content is clean
                         console.log("BG: Waiting for clean content before capture...");
                         await new Promise(resolve => setTimeout(resolve, 100));

                         // Capture the visible area with maximum quality for selected area cropping
                         const visibleImageDataUrl = await chrome.tabs.captureVisibleTab(windowId, {
                             format: 'png',
                             quality: 100  // Maximum quality for selected area capture
                         });

                         // Send to content script for cropping (Image API available there)
                         const cropResult = await chrome.tabs.sendMessage(tabId, {
                             action: 'cropImageToSelection',
                             imageDataUrl: visibleImageDataUrl,
                             selectionData: selectionData,
                             _sourceExtensionId: chrome.runtime.id
                         });

                         if (!cropResult.success) {
                             throw new Error("Image cropping failed: " + (cropResult.error || "Unknown error"));
                         }

                         imageDataUrl = cropResult.croppedImageDataUrl;
                         console.log("BG: Selected area capture successful.");
                    } else {
                         imageDataUrl = await chrome.tabs.captureVisibleTab(windowId, { format: 'png', quality: quality });
                    }

                    if (!imageDataUrl) {
                         throw new Error("Failed to capture image data in either mode.");
                    }

                    // **Step 3: Use original image (no overlays)**
                    let finalImageDataUrl = imageDataUrl; // Use original image
                    // --- End Image Processing ---

                    // **Step 4: Handle Response/Upload**
                    // ... (Existing response/upload logic using finalImageDataUrl) ...
                     if (captureOptions.actionAfterCapture === 'download') {
                        // Check if function exists before calling
                        if (typeof uploadOrDownloadDataUrl === 'function') {
                             uploadOrDownloadDataUrl(finalImageDataUrl, generateScreenshotFilenamePrefix() + '.png')
                                 .then(uploadResult => {
                                     sendResponse({ success: true, message: 'Download initiated/Upload requested.', ...uploadResult });
                                 })
                                 .catch(err => {
                                     console.error("BG: Error during screenshot upload/download:", err);
                                     sendResponse({ success: false, error: err.message || 'Failed to upload/download screenshot.' });
                                 });
                        } else {
                             console.error("BG: uploadOrDownloadDataUrl function missing! Falling back to simple download.");
                             const filenamePrefix = `Stashy_Screenshot_${new Date().toISOString().replace(/[:\-]/g, '').split('.')[0]}`;
                             const filename = `${filenamePrefix}.png`;
                             chrome.downloads.download({ url: finalImageDataUrl, filename: filename, saveAs: false }, (downloadId) => {
                                 if (chrome.runtime.lastError) { console.error("BG: Fallback download failed:", chrome.runtime.lastError); sendResponse({ success: false, error: `Download failed: ${chrome.runtime.lastError.message}` }); }
                                 else if (downloadId === undefined) { console.warn("BG: Fallback download did not start (downloadId undefined)."); sendResponse({ success: false, error: "Download did not start." }); }
                                 else { console.log("BG: Fallback download started with ID:", downloadId); sendResponse({ success: true, message: "Download started." }); }
                             });
                        }
                    } else { // Default is 'annotate' (send back to content script)
                         sendResponse({ success: true, imageDataUrl: finalImageDataUrl });
                    }

                } catch (error) {
                    console.error("BG: Error during screenshot capture/overlay process:", error);

                    // Provide user-friendly error messages
                    let userFriendlyError = error.message || "Capture or overlay failed";

                    if (error.message && error.message.includes("permission")) {
                        userFriendlyError = "Screenshot permission required. Please reload the extension and try again";
                    } else if (error.message && error.message.includes("debugger")) {
                        userFriendlyError = "Unable to capture full page. Please try capturing visible area instead";
                    } else if (error.message && error.message.includes("tab")) {
                        userFriendlyError = "Cannot capture screenshot from this tab. Please try on a different page";
                    } else if (error.message && error.message.includes("network")) {
                        userFriendlyError = "Network error during screenshot capture. Please try again";
                    }

                    sendResponse({
                        success: false,
                        error: userFriendlyError,
                        technicalError: error.message,
                        suggestion: "Try reloading the page and extension if the problem persists"
                    });
                } finally {
                    // **Step 5: Show UI again if it was hidden**
                    if (hideStashy) {
                         console.log(`BG: Requesting content script in tab ${tabId} to show UI...`);
                         chrome.tabs.sendMessage(tabId, {
                             action: 'showUIAfterCapture',
                             _sourceExtensionId: chrome.runtime.id // Add extension ID for validation
                         })
                             .catch(e => console.warn(`BG: Error messaging content script to show UI (tab ${tabId}): ${e?.message || e}`));
                    }
                }
            };
            // --- End of async function definition ---

            performCapture(); // Execute the async capture logic

            break; // End captureScreenshotWithOptions case

        case 'processAudioTranscription':
            // Validate sender - this should come from our extension
            if (!validateMessageSender(sender, true)) {
                console.warn(`BG: Ignoring 'processAudioTranscription' from unexpected sender:`, sender);
                sendResponse({ success: false, error: "Unauthorized sender for audio transcription." });
                return true; // Keep async flag true
            }

            isResponseAsync = true;
            console.log(`BG: Received request to process audio transcription with ${message.provider}`);

            // Process the audio transcription
            processAudioTranscription(message.provider, message.audioDataUrl, message.options)
                .then(result => {
                    console.log(`BG: Audio transcription processing completed with success=${result.success}`);
                    sendResponse(result);
                })
                .catch(error => {
                    console.error(`BG: Error processing audio transcription:`, error);
                    sendResponse({
                        success: false,
                        error: error.message || 'Unknown error during audio transcription'
                    });
                });
            break;

        case 'uploadScreenshotToDrive':
            // Validate sender - this should come from our extension
            if (!validateMessageSender(sender)) {
                console.warn(`BG: Ignoring 'uploadScreenshotToDrive' from unexpected sender:`, sender);
                sendResponse({ success: false, error: "Unauthorized sender for screenshot upload." });
                return true; // Keep async flag true
            }

            isResponseAsync = true;
            console.log("BG: Received uploadScreenshotToDrive request.");
            const { imageDataUrl, filename, folderName, useAppDataFolder } = message;
            if (!imageDataUrl || !filename) {
                sendResponse({ success: false, error: "Missing image data or filename." });
                break; // Exit early
            }

            // Use provided folder name or default
            const screenshotFolderName = folderName || 'Stashy Screenshots';
            // Use provided useAppDataFolder setting or default to false (visible folder)
            const usePrivateFolder = useAppDataFolder !== undefined ? useAppDataFolder : false;

            // Get storage preferences for auto-delete setting
            chrome.storage.local.get(['screenshotStoragePrefs'], async (result) => {
                const prefs = result.screenshotStoragePrefs || {
                    autoDeleteAfter: 0 // Default: never auto-delete
                };

                try {
                    const token = await getAuthToken(false); // Get non-interactive token
                    const blob = dataURLtoBlob(imageDataUrl); // Use helper

                    if (!blob) {
                        throw new Error("Failed to convert Data URL to Blob.");
                    }

                    const driveFile = await uploadScreenshotBlob(token, blob, filename, screenshotFolderName, usePrivateFolder);

                    // Schedule auto-deletion if enabled
                    if (prefs.autoDeleteAfter > 0) {
                        await scheduleScreenshotDeletion(driveFile.id, prefs.autoDeleteAfter);
                    }

                    // Send back success and Drive file info
                    sendResponse({
                        success: true,
                        fileId: driveFile.id,
                        webViewLink: driveFile.webViewLink, // May be null for appDataFolder
                        webContentLink: driveFile.webContentLink // May be null
                    });
                } catch (error) {
                    console.error("BG: Error during screenshot upload process:", error);
                    // Attempt to clear bad token if auth error detected
                    if (error.message.includes("Auth token invalid")) {
                        removeCachedAuthToken(currentToken); // currentToken might be stale here, but worth trying
                        chrome.storage.local.set({ driveSyncEnabled: false }); // Disable sync on auth error
                        notifyPopups('updateConnectionStatus', { isConnected: false });
                        updateAndNotifyStatus('disconnected', 'Auth failed during upload');
                    }
                    sendResponse({ success: false, error: error.message || "Unknown upload error." });
                }
            });
            break; // Keep async flag true

        case 'downloadScreenshot':
            // Validate sender - this should come from our extension
            if (!validateMessageSender(sender)) {
                console.warn(`BG: Ignoring 'downloadScreenshot' from unexpected sender:`, sender);
                sendResponse({ success: false, error: "Unauthorized sender for screenshot download." });
                return true; // Keep async flag true
            }

            isResponseAsync = true;
            console.log("BG: Received downloadScreenshot request.");
            const { imageDataUrl: downloadImageDataUrl, filename: downloadFilename } = message;
            if (!downloadImageDataUrl || !downloadFilename) {
                sendResponse({ success: false, error: "Missing image data or filename." });
                break; // Exit early
            }

            // Download the screenshot
            chrome.downloads.download({
                url: downloadImageDataUrl,
                filename: downloadFilename,
                saveAs: false
            }, (downloadId) => {
                if (chrome.runtime.lastError) {
                    console.error("BG: Download failed:", chrome.runtime.lastError);
                    sendResponse({ success: false, error: `Download failed: ${chrome.runtime.lastError.message}` });
                } else if (downloadId === undefined) {
                    console.warn("BG: Download did not start (downloadId undefined).");
                    sendResponse({ success: false, error: "Download did not start." });
                } else {
                    console.log("BG: Download started with ID:", downloadId);
                    sendResponse({ success: true, downloadId: downloadId });
                }
            });
            break; // Keep async flag true

        case 'getDriveSyncStatus': // Simple check for content script
             // Validate sender - this should come from our extension
             if (!validateMessageSender(sender)) {
                 console.warn(`BG: Ignoring 'getDriveSyncStatus' from unexpected sender:`, sender);
                 sendResponse({ driveEnabled: false, error: "Unauthorized sender" });
                 return true; // Keep async flag true
             }

             isResponseAsync = true; // Use async response as we access storage
             chrome.storage.local.get('driveSyncEnabled', (result) => {
                 sendResponse({ driveEnabled: result.driveSyncEnabled === true });
             });
             break; // Keep async flag true

        // --- Note Deletion from Drive (Triggered from Dashboard) ---
        case 'deleteNotesFromDrive':
             // Validate sender - this should only come from our extension UI
             if (!validateMessageSender(sender)) {
                 console.warn(`BG: Ignoring 'deleteNotesFromDrive' from unexpected sender:`, sender);
                 sendResponse({ success: false, error: "Unauthorized sender for note deletion." });
                 return true; // Keep async flag true
             }

             isResponseAsync = true;
             const noteKeysToDelete = message.noteKeys;
             if (!Array.isArray(noteKeysToDelete) || noteKeysToDelete.length === 0) {
                 sendResponse({ success: false, error: "No note keys provided for deletion." });
                 break;
             }
             console.log(`BG: Received request to delete ${noteKeysToDelete.length} notes from Drive.`);
             updateAndNotifyStatus('syncing', 'Deleting notes from Drive...');

             getAuthToken(false)
                 .then(async token => {
                     let errors = 0;
                     for (const key of noteKeysToDelete) {
                         const filename = keyToFilename(key);
                         if (!filename) {
                             console.warn(`BG: Invalid filename for key ${key}, skipping Drive delete.`);
                             continue;
                         }
                         try {
                             // Find the file ID first
                             const query = `name='${filename}' and 'appDataFolder' in parents and trashed=false`;
                             const listUrl = `${DRIVE_API_BASE_URL}/files?q=${encodeURIComponent(query)}&spaces=appDataFolder&fields=files(id)`;
                             const listResponse = await fetchWithRetry(listUrl, { headers: { 'Authorization': `Bearer ${token}` } });

                             // Handle 401 within the loop
                             if (listResponse.status === 401) {
                                 await removeCachedAuthToken(token);
                                 chrome.storage.local.set({ driveSyncEnabled: false });
                                 notifyPopups('updateConnectionStatus', { isConnected: false });
                                 updateAndNotifyStatus('disconnected', 'Auth failed during delete');
                                 throw new Error("Authentication failed during batch delete."); // Stop the loop
                             }
                             if (!listResponse.ok && listResponse.status !== 404) { // Ignore 404 (already gone)
                                throw new Error(`Drive List Error during delete for ${filename}: ${listResponse.status}`);
                             }
                             const listResult = await listResponse.json();

                             if (listResult.files && listResult.files.length > 0) {
                                 const fileId = listResult.files[0].id;
                                 console.log(`BG: Found file ${filename} (ID: ${fileId}) for deletion.`);
                                 const deleteUrl = `${DRIVE_API_BASE_URL}/files/${fileId}`;
                                 const deleteResponse = await fetchWithRetry(deleteUrl, { method: 'DELETE', headers: { 'Authorization': `Bearer ${token}` } });

                                 // Handle 401 on DELETE
                                 if (deleteResponse.status === 401) {
                                     await removeCachedAuthToken(token);
                                     chrome.storage.local.set({ driveSyncEnabled: false });
                                     notifyPopups('updateConnectionStatus', { isConnected: false });
                                     updateAndNotifyStatus('disconnected', 'Auth failed during delete');
                                     throw new Error("Authentication failed during batch delete."); // Stop the loop
                                 }
                                 if (!deleteResponse.ok && deleteResponse.status !== 404) { // Ignore 404
                                     throw new Error(`Drive Delete Error for ${filename}: ${deleteResponse.status}`);
                                 }
                                 console.log(`BG: Deleted ${filename} (ID: ${fileId}) from Drive.`);
                             } else {
                                 console.log(`BG: File ${filename} not found in Drive for deletion (already deleted?).`);
                             }
                         } catch (error) {
                             console.error(`BG: Failed to delete ${filename} from Drive:`, error);
                             errors++;
                              // Re-check for auth error message specifically if caught here
                             if (error.message.includes("Auth token invalid") || error.message.includes("Authentication failed")) {
                                 throw error; // Re-throw to be caught by the outer catch block which handles disabling sync
                             }
                         }
                     } // end for loop
                     if (errors > 0) {
                         updateAndNotifyStatus('error', `Finished deleting with ${errors} error(s).`);
                         return { success: false, error: `Failed to delete ${errors} file(s) from Drive.` };
                     } else {
                         updateAndNotifyStatus('synced', 'Notes deleted from Drive.');
                         return { success: true };
                     }
                 })
                 .then(result => sendResponse(result))
                 .catch(error => {
                     console.error("BG: Overall error during Drive note deletion:", error);
                     // Auth failure handling is now inside the main catch for sync/upload errors
                     if (error.message.includes("Authentication failed")) {
                         // The auth handling (token removal, disable sync) should have already happened
                     } else {
                         updateAndNotifyStatus('error', 'Drive deletion failed.');
                     }
                     sendResponse({ success: false, error: error.message || "Unknown error during Drive deletion." });
                 });
             break; // Keep async flag true


        case 'generatePdf':
            // Validate sender - this should come from our extension
            if (!validateMessageSender(sender)) {
                console.warn(`BG: Ignoring 'generatePdf' from unexpected sender:`, sender);
                sendResponse({ success: false, error: "Unauthorized sender for PDF generation." });
                return true; // Keep async flag true
            }

            console.log("BG: Received generatePdf message", message);
            isResponseAsync = true;
            generatePdf(message.data, message.options)
                .then(result => {
                    console.log("BG: PDF generation completed", result);
                    sendResponse(result);
                })
                .catch(error => {
                    console.error("BG: Error generating PDF:", error);
                    sendResponse({ success: false, error: error.message || 'Unknown error' });
                });
            break;

        // PDF download functionality has been removed





        case 'downloadImageFile':
            // Validate sender - this should come from our extension
            if (!validateMessageSender(sender)) {
                console.warn(`BG: Ignoring 'downloadImageFile' from unexpected sender:`, sender);
                sendResponse({ success: false, error: "Unauthorized sender for image download." });
                return true; // Keep async flag true
            }

            isResponseAsync = true;
            console.log("BG: Received downloadImageFile request");
            const imageUrl = message.imageUrl;
            const imageFilename = message.filename || `Stashy_Image_${new Date().toISOString().replace(/[:\-]/g, '').split('.')[0]}.png`;

            if (!imageUrl) {
                console.error("BG: No image URL provided for download");
                sendResponse({ success: false, error: "No image URL provided" });
                break;
            }

            // Download the image
            chrome.downloads.download({
                url: imageUrl,
                filename: imageFilename,
                saveAs: false
            }, (downloadId) => {
                if (chrome.runtime.lastError) {
                    console.error("BG: Image download failed:", chrome.runtime.lastError);
                    sendResponse({ success: false, error: `Download failed: ${chrome.runtime.lastError.message}` });
                } else if (downloadId === undefined) {
                    console.warn("BG: Image download did not start (downloadId undefined).");
                    sendResponse({ success: false, error: "Download did not start." });
                } else {
                    console.log("BG: Image download started with ID:", downloadId);
                    sendResponse({ success: true, downloadId: downloadId });
                }
            });
            break; // Keep async flag true

        // --- Premium Status & Payment Integration Handlers ---
        case 'getUserInfo':
            // Handle user info requests from content script on website
            if (!validateMessageSender(sender, false)) {
                console.warn(`BG: Ignoring 'getUserInfo' from unexpected sender:`, sender);
                sendResponse({ success: false, error: 'Unauthorized sender' });
                return false;
            }

            isResponseAsync = true;
            getUserInfo()
                .then(userInfo => {
                    console.log("BG: Successfully retrieved user info for website");
                    sendResponse(userInfo);
                })
                .catch(error => {
                    console.error("BG: Error getting user info for website:", error);
                    sendResponse({ success: false, error: error.message });
                });
            break;

        case 'getPremiumStatus':
            // Handle premium status requests from extension UI or website
            if (!validateMessageSender(sender, false)) {
                console.warn(`BG: Ignoring 'getPremiumStatus' from unexpected sender:`, sender);
                sendResponse({ isPremium: false, error: 'Unauthorized sender' });
                return false;
            }

            isResponseAsync = true;
            checkPremiumStatus()
                .then(premiumStatus => {
                    console.log("BG: Successfully retrieved premium status:", premiumStatus);
                    sendResponse(premiumStatus);
                })
                .catch(error => {
                    console.error("BG: Error getting premium status:", error);
                    sendResponse({ isPremium: false, error: error.message });
                });
            break;

        case 'refreshPremiumStatus':
            // Handle premium status refresh requests (e.g., after payment)
            if (!validateMessageSender(sender, false)) {
                console.warn(`BG: Ignoring 'refreshPremiumStatus' from unexpected sender:`, sender);
                sendResponse({ isPremium: false, error: 'Unauthorized sender' });
                return false;
            }

            isResponseAsync = true;
            refreshPremiumStatus()
                .then(premiumStatus => {
                    console.log("BG: Successfully refreshed premium status:", premiumStatus);
                    sendResponse(premiumStatus);
                })
                .catch(error => {
                    console.error("BG: Error refreshing premium status:", error);
                    sendResponse({ isPremium: false, error: error.message });
                });
            break;

        default:
            // console.log("BG: Received unknown message action:", message.action); // Less noisy
            // Returning false or undefined indicates a synchronous response (or no response)
             return false;
    }
    // Return true if we intend to use sendResponse asynchronously
    return isResponseAsync;
});


/** Generates a filename prefix for screenshots with timestamp */
function generateScreenshotFilenamePrefix() {
    return `Stashy_Screenshot_${new Date().toISOString().replace(/[:\-]/g, '').split('.')[0]}`;
}

/**
 * Schedules a screenshot for deletion after the specified number of days
 * @param {string} fileId - The Drive file ID of the screenshot
 * @param {number} daysToKeep - Number of days to keep the screenshot before deletion
 * @returns {Promise<void>}
 */
async function scheduleScreenshotDeletion(fileId, daysToKeep) {
    if (!fileId || daysToKeep <= 0) {
        return; // Don't schedule if no file ID or days to keep is 0 (keep forever)
    }

    try {
        console.log(`BG: Scheduling screenshot ${fileId} for deletion in ${daysToKeep} days`);

        // Calculate deletion time
        const deletionTime = Date.now() + (daysToKeep * 24 * 60 * 60 * 1000);

        // Get existing scheduled deletions
        const result = await chrome.storage.local.get(['scheduledScreenshotDeletions']);
        const scheduledDeletions = result.scheduledScreenshotDeletions || [];

        // Add new deletion
        scheduledDeletions.push({
            fileId: fileId,
            deletionTime: deletionTime,
            scheduledAt: Date.now()
        });

        // Save updated scheduled deletions
        await chrome.storage.local.set({ scheduledScreenshotDeletions: scheduledDeletions });
        console.log(`BG: Screenshot ${fileId} scheduled for deletion at ${new Date(deletionTime).toLocaleString()}`);
    } catch (error) {
        console.error(`BG: Error scheduling screenshot deletion:`, error);
    }
}

/**
 * Uploads a data URL to Google Drive or downloads it locally based on sync settings
 * @param {string} dataUrl - The data URL to upload/download
 * @param {string} filename - The filename to use
 * @param {Object} options - Additional options for upload
 * @param {string} options.folderName - The name of the folder to upload to
 * @param {boolean} options.useAppDataFolder - Whether to use the appDataFolder (private) or My Drive (visible)
 * @param {boolean} options.downloadLocally - Whether to also download locally when uploading to Drive
 * @returns {Promise<Object>} - Result object with success/error info
 */
async function uploadOrDownloadDataUrl(dataUrl, filename, options = {}) {
    console.log(`BG: uploadOrDownloadDataUrl called for ${filename}`);

    try {
        // Get screenshot storage preferences
        const storagePrefs = await chrome.storage.local.get(['screenshotStoragePrefs']);
        const prefs = storagePrefs.screenshotStoragePrefs || {
            storageLocation: 'local',
            driveFolderName: 'Stashy Screenshots',
            useAppDataFolder: true,
            autoDeleteAfter: 0,
            askBeforeUpload: true
        };

        // Extract options with defaults from preferences
        const folderName = options.folderName || prefs.driveFolderName;
        const useAppDataFolder = options.useAppDataFolder !== undefined ? options.useAppDataFolder : prefs.useAppDataFolder;
        const downloadLocally = options.downloadLocally !== undefined ? options.downloadLocally : (prefs.storageLocation === 'both');

        // Check if Drive sync is enabled and if we should upload to Drive
        const result = await chrome.storage.local.get('driveSyncEnabled');
        const driveEnabled = result.driveSyncEnabled === true;
        const shouldUploadToDrive = driveEnabled && (prefs.storageLocation === 'drive' || prefs.storageLocation === 'both');
        const shouldDownloadLocally = prefs.storageLocation === 'local' || prefs.storageLocation === 'both' || downloadLocally;

        if (shouldUploadToDrive) {
            // Try to upload to Drive
            try {
                console.log(`BG: Drive sync enabled, attempting to upload ${filename} to Drive folder "${folderName}" (private: ${useAppDataFolder})`);
                const token = await getAuthToken(false); // Non-interactive token
                const blob = dataURLtoBlob(dataUrl);

                if (!blob) {
                    throw new Error("Failed to convert data URL to blob");
                }

                const driveFile = await uploadScreenshotBlob(token, blob, filename, folderName, useAppDataFolder);
                console.log(`BG: Successfully uploaded ${filename} to Drive (ID: ${driveFile.id})`);

                // Schedule auto-deletion if enabled
                if (prefs.autoDeleteAfter > 0) {
                    await scheduleScreenshotDeletion(driveFile.id, prefs.autoDeleteAfter);
                }

                // Also download locally if needed
                let downloadId = null;
                if (shouldDownloadLocally) {
                    downloadId = await chrome.downloads.download({
                        url: dataUrl,
                        filename: filename,
                        saveAs: false
                    });
                    console.log(`BG: Also downloaded ${filename} locally (ID: ${downloadId})`);
                }

                return {
                    success: true,
                    uploaded: true,
                    downloaded: shouldDownloadLocally,
                    downloadId: downloadId,
                    fileId: driveFile.id,
                    webViewLink: driveFile.webViewLink,
                    webContentLink: driveFile.webContentLink,
                    folderName: driveFile.folderName,
                    isPrivate: driveFile.isPrivate
                };
            } catch (uploadError) {
                console.error(`BG: Error uploading to Drive:`, uploadError);

                // If upload fails, fall back to local download
                console.log(`BG: Falling back to local download for ${filename}`);
                const downloadId = await chrome.downloads.download({
                    url: dataUrl,
                    filename: filename,
                    saveAs: false
                });

                return {
                    success: true,
                    uploaded: false,
                    downloaded: true,
                    downloadId: downloadId,
                    error: `Drive upload failed: ${uploadError.message}`
                };
            }
        } else if (shouldDownloadLocally) {
            // Just download locally
            console.log(`BG: Drive sync disabled or local storage selected, downloading ${filename} locally`);
            const downloadId = await chrome.downloads.download({
                url: dataUrl,
                filename: filename,
                saveAs: false
            });

            return {
                success: true,
                uploaded: false,
                downloaded: true,
                downloadId: downloadId
            };
        } else {
            // Neither Drive nor local storage is enabled - this shouldn't happen
            console.warn(`BG: Neither Drive nor local storage is enabled for ${filename}`);
            return {
                success: false,
                uploaded: false,
                downloaded: false,
                error: "No storage location enabled"
            };
        }
    } catch (error) {
        console.error(`BG: Error in uploadOrDownloadDataUrl:`, error);
        throw error;
    }
}

// --- Premium Status Management & Payment Integration ---

const PAYMENT_CONFIG = {
    // Your deployed Google Cloud Function URL
    apiBaseUrl: 'https://asia-south1-stashy-app-1e5d1.cloudfunctions.net/stashyApi',
    checkStatusEndpoint: '/checkStatus/'
};

/**
 * Gets user's Google info using the chrome.identity API.
 * Caches the result for 1 hour to avoid excessive auth prompts.
 * @returns {Promise<{success: boolean, userId?: string, email?: string, error?: string}>}
 */
async function getUserInfo() {
    try {
        const cachedUser = await chrome.storage.session.get('stashy_user_info');
        const now = Date.now();

        // Use cache if it exists and is less than 1 hour old
        if (cachedUser.stashy_user_info && (now - cachedUser.stashy_user_info.timestamp < 3600000)) {
            console.log("BG: Returning cached user info");
            return { success: true, ...cachedUser.stashy_user_info.profile };
        }

        console.log("BG: No valid cache. Fetching fresh user info...");
        // Get OAuth2 token. The `interactive: true` flag will prompt the user to sign in if needed.
        const token = await chrome.identity.getAuthToken({ interactive: true });
        if (!token) {
            throw new Error("Authentication flow was cancelled or failed");
        }

        // Use the token to get user's profile info
        const response = await fetch('https://www.googleapis.com/oauth2/v2/userinfo', {
            headers: { 'Authorization': 'Bearer ' + token }
        });

        if (!response.ok) {
            // If the token is bad, remove it from the cache
            chrome.identity.removeCachedAuthToken({ token });
            throw new Error(`Google API responded with status: ${response.status}`);
        }

        const profile = await response.json();
        const userInfo = {
            userId: profile.id, // The unique, permanent Google User ID
            email: profile.email
        };

        // Cache the new user info with a timestamp
        await chrome.storage.session.set({
            stashy_user_info: { profile: userInfo, timestamp: Date.now() }
        });

        console.log("BG: Successfully retrieved and cached user info");
        return { success: true, ...userInfo };
    } catch (error) {
        console.error("BG: Error getting user info:", error);
        return { success: false, error: error.message };
    }
}

/**
 * Checks the user's premium status against your backend.
 * Caches the status for 5 minutes to reduce server calls.
 * @returns {Promise<{isPremium: boolean, expiryDate?: string}>}
 */
async function checkPremiumStatus() {
    const user = await getUserInfo();
    if (!user.success) {
        console.log("BG: User not authenticated, returning free status");
        return { isPremium: false }; // Not signed in, so not premium.
    }

    const userId = user.userId;
    const cacheKey = `premium_status_${userId}`;

    // Check session cache first
    const cachedStatus = await chrome.storage.session.get(cacheKey);
    const now = Date.now();

    if (cachedStatus[cacheKey] && (now - cachedStatus[cacheKey].timestamp < 300000)) { // 5-minute cache
        console.log("BG: Returning cached premium status:", cachedStatus[cacheKey].data);
        return cachedStatus[cacheKey].data;
    }

    try {
        console.log(`BG: Fetching premium status for user ${userId}...`);
        const response = await fetch(`${PAYMENT_CONFIG.apiBaseUrl}${PAYMENT_CONFIG.checkStatusEndpoint}${userId}`);

        if (!response.ok) {
            throw new Error(`Backend responded with status: ${response.status}`);
        }

        const statusData = await response.json(); // { premium: boolean, expiryDate: string }
        const premiumInfo = { isPremium: statusData.premium, expiryDate: statusData.expiryDate };

        // Cache the result
        await chrome.storage.session.set({
            [cacheKey]: { data: premiumInfo, timestamp: now }
        });

        console.log("BG: Successfully retrieved premium status:", premiumInfo);
        return premiumInfo;
    } catch (error) {
        console.error("BG: Could not check premium status:", error);
        // In case of network/server error, it's safer to assume not premium.
        return { isPremium: false };
    }
}

/**
 * Forces a refresh of the premium status cache
 * @returns {Promise<{isPremium: boolean, expiryDate?: string}>}
 */
async function refreshPremiumStatus() {
    const user = await getUserInfo();
    if (!user.success) {
        return { isPremium: false };
    }

    const userId = user.userId;
    const cacheKey = `premium_status_${userId}`;

    // Clear the cache
    await chrome.storage.session.remove(cacheKey);

    // Fetch fresh status
    return await checkPremiumStatus();
}

console.log("Stashy Background Script Loaded (Premium Integration Added)");

// --- END OF FILE background.js ---